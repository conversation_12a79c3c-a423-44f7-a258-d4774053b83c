#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用您提供的完整HTML测试表格合并单元格处理
"""

from lxml import etree, html as lxmlhtml

def build_table_matrix(data_rows):
    """构建表格矩阵来处理合并单元格"""
    if not data_rows:
        return []
        
    # 预估最大列数
    max_cols = 0
    for row in data_rows:
        cells = row.xpath('.//td')
        col_count = sum(int(cell.get('colspan', 1)) for cell in cells)
        max_cols = max(max_cols, col_count)
    
    # 初始化矩阵
    matrix = []
    for row_idx, row in enumerate(data_rows):
        if row_idx >= len(matrix):
            matrix.append([None] * max_cols)
        
        cells = row.xpath('.//td')
        col_idx = 0
        
        for cell in cells:
            # 找到下一个空位置
            while col_idx < len(matrix[row_idx]) and matrix[row_idx][col_idx] is not None:
                col_idx += 1
            
            if col_idx >= len(matrix[row_idx]):
                break
                
            colspan = int(cell.get('colspan', 1))
            rowspan = int(cell.get('rowspan', 1))
            
            # 填充当前单元格占用的所有位置
            for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                # 确保有足够的行
                while r >= len(matrix):
                    matrix.append([None] * max_cols)
                
                for c in range(col_idx, min(col_idx + colspan, max_cols)):
                    if c < len(matrix[r]):
                        matrix[r][c] = {
                            'cell': cell,
                            'row_idx': row_idx,
                            'col_idx': col_idx,
                            'colspan': colspan,
                            'rowspan': rowspan,
                            'is_origin': (r == row_idx and c == col_idx),
                            'text': cell.text_content().strip()
                        }
            
            col_idx += colspan
    
    return matrix

def group_logical_rows(table_matrix, data_rows):
    """根据合并单元格将行分组为逻辑行组"""
    if not table_matrix or not data_rows:
        return []
        
    groups = []
    processed_rows = set()
    
    for row_idx in range(len(data_rows)):
        if row_idx in processed_rows:
            continue
            
        # 找到当前行涉及的所有行（由于rowspan）
        involved_rows = set([row_idx])
        
        if row_idx < len(table_matrix):
            for cell_info in table_matrix[row_idx]:
                if cell_info and cell_info['is_origin']:
                    rowspan = cell_info['rowspan']
                    for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                        involved_rows.add(r)
        
        groups.append(sorted(involved_rows))
        processed_rows.update(involved_rows)
    
    return groups

def reconstruct_table_html(data_rows, row_indices, table_matrix):
    """重构表格HTML，保持合并单元格的结构"""
    if not row_indices:
        return ""
        
    # 收集所有需要的单元格
    cells_to_include = set()
    for row_idx in row_indices:
        if row_idx < len(table_matrix):
            for cell_info in table_matrix[row_idx]:
                if cell_info and cell_info['is_origin']:
                    cells_to_include.add(id(cell_info['cell']))
    
    # 重构HTML
    reconstructed_rows = []
    for row_idx in row_indices:
        if row_idx >= len(data_rows):
            continue
            
        row = data_rows[row_idx]
        cells = row.xpath('.//td')
        
        # 只包含属于当前逻辑组的单元格
        filtered_cells = [cell for cell in cells if id(cell) in cells_to_include]
        
        if filtered_cells:
            # 重构行HTML
            row_html = "<tr>"
            for cell in filtered_cells:
                cell_html = lxmlhtml.tostring(cell, encoding="unicode", pretty_print=False)
                row_html += cell_html
            row_html += "</tr>"
            reconstructed_rows.append(row_html)
    
    return "".join(reconstructed_rows)

def test_full_table():
    """测试完整的表格处理"""
    
    # 您提供的测试HTML的关键部分
    test_html = """
    <table>
        <tr>
            <td colspan="2" rowspan="3"><b><font><br><img src="1_html_b4e0157c1e7c863a.png" hspace="33" vspace="14">
            </font></b></td>
            <td colspan="3" rowspan="3"><b><font><br><img src="1_html_af8480bcd7f2a3e0.png" hspace="91" vspace="12">
            </font></b></td>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
            <td><font><br></font></td>
            <td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
        </tr>
        <tr>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
            <td><font><br></font></td>
            <td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
        </tr>
        <tr>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
            <td><font><br></font></td>
            <td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
        </tr>
        <tr>
            <td><font>客户名称</font></td>
            <td colspan="2"><font><br></font></td>
            <td><font>报价单位名称</font></td>
            <td sdnum="1033;0;#,##0_ "><font>达观数据有限公司</font></td>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
            <td sdnum="1033;0;#,##0_ "><font><br></font></td>
        </tr>
        <tr>
            <td rowspan="14"><b><font>基础模块-机器人<br>Windows版</font></b></td>
            <td rowspan="7"><font>执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</font></td>
            <td><font>流程任务拉取</font></td>
            <td rowspan="14"><font>必选</font></td>
            <td rowspan="7"><font>个</font></td>
            <td rowspan="7" sdval="45000" sdnum="1033;0;#,##0_ "><font>  </font></td>
            <td rowspan="7"><font><br></font></td>
            <td rowspan="7" sdnum="1033;0;#,##0_ "><font><br></font></td>
        </tr>
        <tr>
            <td><font>流程任务运行</font></td>
        </tr>
        <tr>
            <td><font>流程任务数据上报</font></td>
        </tr>
    </table>
    """
    
    print("开始测试完整表格处理...")
    
    # 解析HTML
    tree = etree.fromstring(test_html, lxmlhtml.HTMLParser())
    table = tree.xpath('//table')[0]
    data_rows = table.xpath('.//tr')
    
    print(f"总共找到 {len(data_rows)} 行")
    
    # 构建矩阵
    matrix = build_table_matrix(data_rows)
    
    # 分组逻辑行
    groups = group_logical_rows(matrix, data_rows)
    
    print(f"\n逻辑行分组结果 (共{len(groups)}组):")
    
    for i, group in enumerate(groups):
        print(f"\n=== 组 {i+1}: 行 {group} ===")
        
        # 重构HTML
        reconstructed_html = reconstruct_table_html(data_rows, group, matrix)
        
        # 提取纯文本
        group_content = []
        for row_idx in group:
            if row_idx < len(data_rows):
                row_text = data_rows[row_idx].text_content().strip()
                if row_text:
                    group_content.append(row_text)
        
        print(f"纯文本内容: {' | '.join(group_content)}")
        print(f"重构的HTML长度: {len(reconstructed_html)} 字符")
        
        # 显示重构HTML的前200个字符
        if reconstructed_html:
            print(f"HTML预览: {reconstructed_html[:200]}...")
        
        # 构建完整表格HTML
        complete_html = f"<table><tbody>{reconstructed_html}</tbody></table>"
        print(f"完整表格HTML长度: {len(complete_html)} 字符")

if __name__ == "__main__":
    test_full_table()
