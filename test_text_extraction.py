#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的文本提取功能
"""

import re
from lxml import etree, html as lxmlhtml

def extract_plain_text_from_html(html_content):
    """
    从HTML表格中提取纯文本，在不同单元格之间添加空格分隔
    """
    if not html_content:
        return ""
    
    # 正则表达式模式
    replace_one_white_pattern = re.compile(r"(?<![a-zA-Z\W0-9])\s(?![a-zA-Z\W0-9])")
    
    try:
        # 解析HTML
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        
        # 提取所有单元格的文本
        cell_texts = []
        
        # 按行处理
        for row in tree.xpath('.//tr'):
            row_texts = []
            for cell in row.xpath('.//td | .//th'):
                # 提取单元格内的所有文本，去除多余空白
                cell_text_fragments = []
                for text in cell.xpath('.//text()[normalize-space() != ""]'):
                    text = replace_one_white_pattern.sub("", text.strip())
                    if text:
                        cell_text_fragments.append(text)
                
                cell_text = " ".join(cell_text_fragments).replace("\n", "")
                if cell_text.strip():
                    row_texts.append(cell_text.strip())
            
            # 将同一行的单元格用空格连接
            if row_texts:
                cell_texts.append(" ".join(row_texts))
        
        # 将不同行用空格连接
        return " ".join(cell_texts)
        
    except Exception as e:
        print(f"HTML解析失败: {e}")
        return ""

def test_text_extraction():
    """测试文本提取功能"""
    
    # 测试用例1：包含合并单元格的表格
    test_html1 = """
    <table>
        <tbody>
            <tr>
                <td rowspan="3">基础模块-机器人<br>Windows版</td>
                <td rowspan="2">执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</td>
                <td>流程任务拉取</td>
                <td rowspan="3">必选</td>
                <td rowspan="2">个</td>
                <td rowspan="2">45000</td>
                <td rowspan="2">1</td>
            </tr>
            <tr>
                <td>流程任务运行</td>
            </tr>
            <tr>
                <td>执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</td>
                <td>流程任务数据上报</td>
                <td>个</td>
                <td>30000</td>
                <td>1</td>
            </tr>
        </tbody>
    </table>
    """
    
    # 测试用例2：客户信息表格
    test_html2 = """
    <table>
        <tbody>
            <tr>
                <td>客户名称</td>
                <td colspan="2"></td>
                <td>报价单位名称</td>
                <td>达观数据有限公司</td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </tbody>
    </table>
    """
    
    # 测试用例3：表头
    test_html3 = """
    <table>
        <tbody>
            <tr>
                <td><b>产品名称</b></td>
                <td><b>类型</b></td>
                <td><b>描述</b></td>
                <td><b>必选/可选</b></td>
                <td><b>单位</b></td>
                <td><b>单价（元）</b></td>
                <td><b>数量</b></td>
                <td><b>总价（元）</b></td>
            </tr>
        </tbody>
    </table>
    """
    
    print("=== 测试文本提取功能 ===\n")
    
    print("测试用例1 - 包含合并单元格的产品信息:")
    result1 = extract_plain_text_from_html(test_html1)
    print(f"提取结果: {result1}")
    print(f"长度: {len(result1)} 字符\n")
    
    print("测试用例2 - 客户信息行:")
    result2 = extract_plain_text_from_html(test_html2)
    print(f"提取结果: {result2}")
    print(f"长度: {len(result2)} 字符\n")
    
    print("测试用例3 - 表头信息:")
    result3 = extract_plain_text_from_html(test_html3)
    print(f"提取结果: {result3}")
    print(f"长度: {len(result3)} 字符\n")
    
    # 验证空格分隔
    print("=== 验证空格分隔效果 ===")
    print("测试用例1中的单元格分隔:")
    cells = result1.split()
    for i, cell in enumerate(cells):
        if cell.strip():
            print(f"  单元格{i+1}: '{cell}'")

if __name__ == "__main__":
    test_text_extraction()
