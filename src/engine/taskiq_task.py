#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from taskiq import TaskiqScheduler
from taskiq.schedule_sources import LabelScheduleSource
from taskiq_redis import RedisAsyncResultBackend, ListQueueBroker, ListRedisScheduleSource

from config import REDIS_PASSWORD, REDIS_HOST, REDIS_PORT, TASKIQ_BROKER_DB


redis_url = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{TASKIQ_BROKER_DB}"

result_backend = RedisAsyncResultBackend(
    redis_url=redis_url,
    result_ex_time=1000
)

broker = ListQueueBroker(
    url=redis_url,
).with_result_backend(result_backend)

redis_schedule_source = ListRedisScheduleSource(url=redis_url) 

scheduler = TaskiqScheduler(
    broker=broker,
    sources=[
        redis_schedule_source,
        LabelScheduleSource(broker=broker)],
)
