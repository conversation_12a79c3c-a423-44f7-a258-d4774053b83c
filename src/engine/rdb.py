import contextlib
import logging
from typing import Annotated, Async<PERSON><PERSON><PERSON>, Iterator
from urllib.parse import quote_plus

import sqlalchemy
from fastapi import Depends
from fastapi.concurrency import run_in_threadpool
from sqlalchemy import create_engine, func, desc
from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker, Session

from common import g
from common.schema import Pager
from config import MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DB, MYSQL_CHARSET


if MYSQL_HOST:
    SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{quote_plus(MYSQL_PASSWORD)}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset={MYSQL_CHARSET}"
    ASYNC_SQLALCHEMY_DATABASE_URL = f"mysql+aiomysql://{MYSQL_USER}:{quote_plus(MYSQL_PASSWORD)}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset={MYSQL_CHARSET}"
    engine_sync = create_engine(
        url=SQLALCHEMY_DATABASE_URL,
        pool_recycle=300,
        pool_size=20,
        max_overflow=15,
        pool_timeout=15,
        echo=False,
    )
    engine = create_async_engine(
        url=ASYNC_SQLALCHEMY_DATABASE_URL,
        pool_recycle=300,
        pool_size=20,
        max_overflow=15,
        pool_timeout=15,
        echo=False,
    )
    session_maker_sync = sessionmaker(bind=engine_sync, autocommit=False, autoflush=False)
    session_maker = async_sessionmaker(bind=engine, autoflush=False, autocommit=False)
    logging.info(f"Connecting to database url={engine_sync}")
else:
    raise Exception("Database configuration is missing")


class DatabaseSessionManager:
    def __init__(self):
        self._session_maker = session_maker
        self._engine_sync = engine_sync
        self._session_maker_sync = session_maker_sync

    @contextlib.asynccontextmanager
    async def session(self) -> AsyncIterator[AsyncSession]:
        if self._session_maker is None:
            raise Exception("DatabaseSessionManager is not initialized")

        session = self._session_maker()
        try:
            yield session
        except Exception:
            await g.session.rollback()
            raise
        finally:
            await g.session.close()

    @contextlib.asynccontextmanager
    async def session_sync(self) -> Iterator[Session]:
        if self._session_maker_sync is None:
            raise Exception("DatabaseSessionManager is not initialized")

        session = await run_in_threadpool(lambda: self._session_maker_sync())
        try:
            yield session
        except Exception:
            await run_in_threadpool(lambda: g.session_sync.rollback())
            raise
        finally:
            await run_in_threadpool(lambda: g.session_sync.close())

    @property
    def session_maker_sync(self):
        return self._session_maker_sync
    
    @property
    def session_maker(self):
        return self._session_maker


sessionmanager = DatabaseSessionManager()


async def get_db():
    async with sessionmanager.session() as session:
        g.session = session
        yield session


async def get_db_sync():
    async with sessionmanager.session_sync() as session:
        g.session_sync = session
        yield session


def load_session_sync_context(func):
    def wrapper(*args, **kwargs):
        session_sync = sessionmanager.session_maker_sync()
        g.session_sync = session_sync
        try:
            return func(*args, **kwargs)
        except Exception:
            g.session_sync.rollback()
            raise
        finally:
            g.session_sync.close()

    return wrapper

def load_session_context(func):
    async def wrapper(*args, **kwargs):
        session = sessionmanager._session_maker()
        g.session = session
        try:
            return await func(*args, **kwargs)
        except Exception:
            await g.session.rollback()
            raise
        finally:
            await g.session.close()

    return wrapper


session_type = Annotated[AsyncSession, Depends(get_db)]
session_type_ync = Annotated[AsyncSession, Depends(get_db_sync)]


async def paginator(query: sqlalchemy.Select, page: int = 1, per_page: int = 20) -> (Pager, list[dict]):
    # 创建一个没有 ORDER BY 子句的计数查询
    count_query = query.order_by(None).with_only_columns(func.count())
    count_result = await g.session.execute(count_query)
    total = count_result.scalar_one_or_none() or 0

    # 分页查询保持不变
    res = await g.session.execute(
        query.offset((page - 1) * per_page).limit(per_page)
    )
    items = [dict(row) for row in res.mappings().all()]
    total_pages = (total + per_page - 1) // per_page

    return Pager(page=page, per_page=per_page, pages=total_pages, total=total), items


def query_order(query: sqlalchemy.Select, order_by: str = None, table=None):
    if order_by:
        order_list = order_by.split(",")
        for order in order_list:
            order_c, order_s = order.split(":")
            if table is not None:
                order_c = getattr(table, order_c)
            if order_s == "desc":
                query = query.order_by(desc(order_c))
            else:
                query = query.order_by(order_c)

    return query


async def fetch_all(query: sqlalchemy.Select | sqlalchemy.TextClause):
    res = await g.session.execute(query)
    return [dict(row) for row in res.mappings().all()]


def fetch_all_sync(query: sqlalchemy.Select | sqlalchemy.TextClause):
    res = g.session_sync.execute(query)
    return [dict(row) for row in res.mappings().all()]


async def fetch_one(query: sqlalchemy.Select | sqlalchemy.TextClause):
    res = await g.session.execute(query)
    row = res.mappings().first()
    return dict(row) if row else {}


def fetch_one_sync(query: sqlalchemy.Select | sqlalchemy.TextClause):
    res = g.session_sync.execute(query)
    row = res.mappings().first()
    return dict(row) if row else {}
