import redis.asyncio as aioredis
import redis

from config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_CACHE_DB

normal_cache_pool = aioredis.ConnectionPool(
    host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, db=REDIS_CACHE_DB, decode_responses=True)
r_cache = aioredis.StrictRedis(connection_pool=normal_cache_pool)

normal_cache_pool_sync = redis.ConnectionPool(
    host=REDIS_HOST, port=REDIS_PORT, password=REDIS_PASSWORD, db=REDIS_CACHE_DB, decode_responses=True)
r_cache_sync = redis.StrictRedis(connection_pool=normal_cache_pool_sync)
