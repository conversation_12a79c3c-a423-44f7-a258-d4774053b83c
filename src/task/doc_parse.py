#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pathlib import Path

from controller.repository import Doc, SupportFormat
from controller.parser.docling_parser import DoclingParser
from controller.parser.mineru_parser import MinerUParser
from controller.parser.chunker import Html<PERSON>hunker, BGE_M3_TOKENIZER


class DocParseOfflineTask:
    @staticmethod
    async def parsing(doc_id: int) -> bool:
        doc = await Doc.get_one(doc_id=doc_id)
        if doc["path"] and Path(doc["path"]).suffix in (
                SupportFormat.pdf,
                SupportFormat.ppt,
                SupportFormat.pptx,
                SupportFormat.doc,
                SupportFormat.docx):
            parser = MinerUParser(
                doc_id=doc_id,
                doc=doc)
        else:
            parser = DoclingParser(
                doc_id=doc_id,
                doc=doc,
                chunker=HtmlChunker(max_tokens=1024, tokenizer=BGE_M3_TOKENIZER),
                # 其余参数由内部读取的策略决定
            )

        await parser.exec()

        # 成功失败状态
        return True if parser.error is None else False


DocParseOffline = DocParseOfflineTask()
