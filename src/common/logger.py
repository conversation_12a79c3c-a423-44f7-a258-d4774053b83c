import sys
import os
import re
import datetime
import logging
import functools
import time
from typing import Optional, Callable, Any

from loguru import logger

from config import LOGGING_LEVEL, LOG_DIR


class InterceptHandler(logging.Handler):
    """
    删除日志颜色

    不同框架底层方法在写入日志时,有可能被loguru判断为带颜色的日志从而进入后续处理,导致写入错误.
    请务必保留此方法,避免日志写入导致错误.
    """
    ansi_escape = re.compile(r"\x1B\[[0-?]*[ -/]*[@-~]")  # 删除自带的颜色

    def emit(self, record):
        logger_opt = logger.opt(depth=6, exception=record.exc_info, colors=False)
        logger_opt.log(record.levelname, self.ansi_escape.sub("", record.getMessage()))


def clear_timeout_logs(log_dir: str, keep_day: int = 15):
    """
    删除最大超过keep_day的日志
    由于loguru本身不具备在删除既往日志的功能(只支持同一进程的日志旋转),添加本方法进行处理
    强依赖数据文件格式包含 yyyy-mm-dd格式
    :param log_dir: 日志路径
    :param keep_day: 最多保留日
    :return:
    """
    pattern = re.compile(r"\d{4}-\d{2}-\d{2}")
    for filename in os.listdir(log_dir):
        search = pattern.search(filename)
        today = datetime.date.today()
        if search:
            filepath = os.path.join(log_dir, filename)
            log_day = datetime.datetime.strptime(search.group(), "%Y-%m-%d").date()
            if (today - log_day).days > keep_day:
                try:
                    os.remove(filepath)
                except Exception as err:
                    logging.info(f"删除超时日志失败: {filepath}: {err}")
                else:
                    logging.info(f"删除超时日志成功: {filepath}")


def init():
    clear_timeout_logs(LOG_DIR, keep_day=15)


# [非常重要]于文件流程配置标准logging,保证在import时完成logging配置初始化,避免被flask-blueprint覆写root-logger
# [标准日志写入loguru] 此配置可将各库写入原生logging的日志配置入loguru,例如Flask
logging.basicConfig(
    handlers=[InterceptHandler(level=LOGGING_LEVEL)],
    level=LOGGING_LEVEL)
# # disabled werkzeug log,由after_request写带trace_id的日志
# logging.getLogger("werkzeug").disabled = True

# [针对Gunicorn] 错误信息输出至控制台
logger.configure(handlers=[{"sink": sys.stderr, "level": LOGGING_LEVEL}])

# [定义日志路径]
os.makedirs(LOG_DIR, exist_ok=True)

# 日志旋转、大小限制、更替等参数均支持多种配置,详情请参考文档
# [logger参数文档: https://loguru.readthedocs.io/en/stable/api/logger.html#loguru._logger.Logger]
# 请务必设置colorize=False,避免在不同系统上由于颜色标签的写入造成问题
if LOGGING_LEVEL == "DEBUG":
    logger.add(LOG_DIR + "/debug_{time:%Y-%m-%d}.log", level="DEBUG", colorize=False, rotation="1 days", retention=7,
               backtrace=False, diagnose=False, encoding="utf-8")
logger.add(LOG_DIR + "/info_{time:%Y-%m-%d}.log", level="INFO", colorize=False, rotation="1 days", retention=7,
           backtrace=False, diagnose=False, encoding="utf-8")
logger.add(LOG_DIR + "/error_{time:%Y-%m-%d}.log", level="ERROR", colorize=False, rotation="1 days", retention=15,
           backtrace=False, diagnose=False, encoding="utf-8")


def async_time_cost(name: Optional[str] = None) -> Callable:
    """装饰器：计算函数执行时间并记录日志

    Args:
        name: 自定义日志名称，如果为None则使用方法名

    Returns:
        装饰器函数
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 使用提供的名称或函数名
            log_name = name or func.__name__
            # 记录开始时间
            start_time = time.time()
            # 执行原函数
            result = await func(*args, **kwargs)
            # 计算耗时
            cost_time = time.time() - start_time
            # 记录日志
            if args and hasattr(args[0], 'log_prefix'):
                logger.info(f"{args[0].log_prefix}{log_name} 耗时 {cost_time:.3f}s")
            else:
                logger.info(f"{log_name} 耗时 {cost_time:.3f}s")
            return result

        return wrapper

    return decorator
