import time
import threading
import os


class IdWorker:
    def __init__(self, worker_id, data_center_id, sequence):
        self.twepoch = 1288834974657
        self.worker_id_bits = 5
        self.data_center_id_bits = 5
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)
        self.max_data_center_id = -1 ^ (-1 << self.data_center_id_bits)
        self.sequence_bits = 12
        self.worker_id_shift = self.sequence_bits
        self.data_center_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.worker_id_bits + self.data_center_id_bits
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        self.last_timestamp = -1
        self.sequence = sequence
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError('worker_id值范围为0-31')
        if data_center_id > self.max_data_center_id or data_center_id < 0:
            raise ValueError('data_center_id值范围为0-31')
        self.worker_id = worker_id
        self.data_center_id = data_center_id
        self.lock = threading.Lock()

    @staticmethod
    def _gen_timestamp():
        return int(time.time() * 1000)

    def _til_next_millis(self, last_timestamp):
        timestamp = self._gen_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._gen_timestamp()
        return timestamp

    def get_id(self) -> int:
        with self.lock:
            timestamp = self._gen_timestamp()
            if timestamp < self.last_timestamp:
                raise Exception('时钟回拨，拒绝生成id')
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.sequence_mask
                if self.sequence == 0:
                    timestamp = self._til_next_millis(self.last_timestamp)
            else:
                self.sequence = 0
            self.last_timestamp = timestamp
            return ((timestamp - self.twepoch) << self.timestamp_left_shift) | (
                    self.data_center_id << self.data_center_id_shift) | (
                    self.worker_id << self.worker_id_shift) | self.sequence


Snowflake = IdWorker(worker_id=int(os.getpid() % 32), data_center_id=1, sequence=0)
