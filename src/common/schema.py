import typing
from typing import Annotated, TypeVar, Any, Callable, Literal

from fastapi import Body, Query, Header, Path
from pydantic import BaseModel, Field, AliasChoices, AliasPath, types
from pydantic.fields import annotated_types, JsonDict, FieldInfo, Deprecated


class Pager(BaseModel):
    per_page: Annotated[int, Field(title="每页数量")]
    page: Annotated[int, Field(title="页码")]
    pages: Annotated[int, Field(title="总页数")]
    total: Annotated[int, Field(title="总数")]
