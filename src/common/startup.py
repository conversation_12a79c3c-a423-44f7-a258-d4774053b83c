from contextlib import asynccontextmanager
from fastapi import FastAPI
from anyio.lowlevel import RunVar
from anyio import CapacityLimiter

from config import SYNC_THREAD_COUNT


def startup():
    RunVar("_default_thread_limiter").set(CapacityLimiter(SYNC_THREAD_COUNT))


@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    startup()
    yield


def register_startup(app: FastAPI):
    # 注意：新的 lifespan 方式需要在 FastAPI 应用创建时设置
    # 这个函数保留是为了兼容性，但实际的 lifespan 应该在创建 FastAPI 实例时传入
    pass
