import traceback
from datetime import datetime

from fastapi import Request, FastAPI
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from common import g
from common.logger import logger
from exception import ApiError, UnknownErrorCode, ParamCheckErrorCode, MESSAGE, status


def register_exception_handler(app: FastAPI):
    @app.middleware("http")
    async def common_requests(request: Request, call_next):
        async def get_request_body():
            try:
                request_body = await request.json()
            except:
                return {}
            return request_body

        # 记录请求开始时间
        start_time = datetime.now()
        # 获取请求信息
        method = request.method
        url = str(request.url.path)
        client_ip = request.client.host
        client_agent = request.headers.get("user-agent")
        query_params = dict(request.query_params)
        request_body = await get_request_body()
        g.request = request
        g.extra_data = {}
        # 处理请求
        try:
            response = await call_next(request)
        except Exception as err:
            logger.error(traceback.format_exc(limit=-4))
            logger.error(
                f"接口异常: HTTP Request {method}: {url}, Query Params: {query_params}, Body: {request_body} "
                f"IP: {client_ip}, Agent: {client_agent}, Start Time: {start_time}, "
                f"用时: {(datetime.now() - start_time).total_seconds():.4f}")

            # 400错误
            if isinstance(err, (RequestValidationError, ValidationError, ResponseValidationError)):
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST, content={"message": str(err), "code": ParamCheckErrorCode})

            # ApiError底层错误
            elif isinstance(err, ApiError):
                return JSONResponse(
                    status_code=err.http_code,
                    content={"message": f"{err.message}", "code": err.code, "trace_id": g.trace_id})

            # 错误兜底
            else:
                code = UnknownErrorCode
                return JSONResponse(
                    status_code=MESSAGE[code]["http_code"],
                    content={"message": f"{err}", "code": code, "trace_id": g.trace_id})

        else:
            logger.info(
                f"HTTP Request {method}: {url}, Query Params: {query_params}, Body: {request_body} "
                f"IP: {client_ip}, Agent: {client_agent}, Start Time: {start_time}, "
                f"用时: {(datetime.now() - start_time).total_seconds():.4f}")
            return response
