import os


# [LLM]
LLM_MAX_TOKEN_LEN = int(os.getenv("LLM_MAX_TOKEN_LEN", 1024 * 60))  # deepseek 官方限制为64K，此处保留一定余量


# [CHAT]
MAX_HISTORY_LEN = int(os.getenv("MAX_HISTORY_LEN", 3))


# [CHUNKING]
DEFAULT_WINDOWS_CHUNK_SIZE = int(os.getenv("DEFAULT_WINDOWS_CHUNK_SIZE", 1024 * 16))
DEFAULT_WINDOWS_OVERLAP_SIZE = int(os.getenv("DEFAULT_WINDOWS_OVERLAP_SIZE", 128 * 4))


# [ABSTRACT]
DEFAULT_DOC_CHUNK_SIZE = int(os.getenv("DEFAULT_ABSTRACT_CHUNK_SIZE", 1024 * 16))
DEFAULT_ABSTRACT_CHUNK_SIZE = int(os.getenv("DEFAULT_ABSTRACT_CHUNK_SIZE", 1024 * 8))
DEFAULT_ABSTRACT_OVERLAP_SIZE = int(os.getenv("DEFAULT_ABSTRACT_OVERLAP_SIZE", 1024 * 2))
