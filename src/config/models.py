from enum import StrEnum


class LLMModel(StrEnum):
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_REASONER = "deepseek-reasoner"
    GPT_41 = "gpt-4.1"
    GPT_5 = "gpt-5"
    GPT_5_MINI = "gpt-5-mini"
    GPT_5_NANO = "gpt-5-nano"
    O4_MINI = "o4-mini"
    QWEN3_30B = "qwen3:30b"
    QWEN3_30B_INSTRUCT = "qwen3:30b-a3b-instruct"
    QWEN3_30B_THINKING = "qwen3:30b-a3b-thinking"
    QWEN3_235B_INSTRUCT = "qwen3:235b-a22b-instruct"
    QWEN3_235B_THINKING = "qwen3:235b-a22b-thinking"
    QWEN_FLASH = "qwen-flash"
    CLAUDE_4_SONNET = "claude-sonnet-4-20250514"
    GEMINI_2_5_FLASH_LITE = "gemini-2.5-flash-lite"
    GEMINI_2_5_FLASH = "gemini-2.5-flash"
    GEMINI_2_5_PRO = "gemini-2.5-pro"
    ARK_DEEPSEEK_V3 = "ark-ep-bi-20250607231633-42c59"
    ARK_DEEPSEEK_R1 = "ark-ep-bi-20250607231521-4qr7p"

class EmbeddingModel(StrEnum):
    BGE_M3 = "bge-m3"
    TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"

class RerankerModel(StrEnum):
    BGE_RERANKER_V2_M3 = "bge-reranker-v2-m3"
