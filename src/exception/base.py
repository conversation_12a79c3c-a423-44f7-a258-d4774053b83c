from exception.error_code import UnknownErrorCode, MESSAGE


class ApiError(Exception):
    """
    标准API异常
    """
    default_code = UnknownErrorCode

    def __init__(self, message=None, code=None, http_code=None, data: (dict, list, str) = None, *args):
        """
        标准API异常构造函数

        :param code: 异常编码
        :param message: 异常信息
        :param http_code: HTTP状态码
        :param args:
        :param kwargs:
        """

        super(ApiError, self).__init__(message, *args)

        self.code = code or self.default_code
        self.message = message or MESSAGE[self.code]["message"]
        self.http_code = http_code or MESSAGE[self.code]["http_code"]
        self.data = data

    def __str__(self):
        return f"""{self.__class__.__name__}(code={self.code}, message={self.message}, http_code={self.http_code})"""
