from fastapi import FastAPI

from config.uv_log_config import CUSTOM_LOGGING_CONFIG
from common.logger import init as logger_init
from common.middleware import register_middleware
from common.startup import lifespan
from view import register_routes

logger_init()


app = FastAPI(lifespan=lifespan)
register_middleware(app=app)
register_routes(app=app)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=10001, log_config=CUSTOM_LOGGING_CONFIG)
