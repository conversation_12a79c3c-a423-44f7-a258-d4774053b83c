#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime

USER_MEMORY_PROMPT = f"""我需要你讲用户的问题根据以下几大维度，抽取出结构简洁、清晰的用户偏好、风格、行为模式、知识背景等信息要素，以便于精准搜索和问答提效。

## 阶段一、确认信息要素

任务：按以下分类进行提取，包括但不限于：
1. 基础偏好: 语言风格、格式喜好
2. 个人信息: 行业、地点、组织、领域、场景
3. 任务习惯: 工具、模板、流程
4. 知识背景: 行业、角色、场景
5. 行为模式: 关注点、排除点、使用习惯、决策风格

## 阶段二、要素新增合并

任务：根据已有要素对，和新抽取的信息要素：
* 与已有的要素信息进行合并，合并后信息等级+1，最高为5.
* 对于具备排他性的信息,冲突时进行更新,比如用户姓名
* 对于无排他性的信息，进行新增，信息等级为1。如果用户要求你记住这个要素，或表达非常明确，则等级为5.

## 阶段三、JSON输出

任务：按照以上要求，根据传入的用户问句和已知要素，进行JSON化输出。
格式为：[{{"facts":"要素分类:要素内容", "level": 1}}]

任务说明完毕，以下是示例：

问句：我是John。职业是工程师。
已知要素：[]
输出：[{{"facts": "用户姓名:John", "level": 5}}, {{"facts": "用户职业:工程师", "level": 5}}]  # 表达明确

问句：我是John。职业是医生。
已知要素：[{{"facts": "用户姓名:John", "level": 5}}, {{"facts": "用户职业:工程师", "level": 5}}]
输出：[{{"facts": "用户姓名:John", "level": 5}}, {{"facts": "用户职业:医生", "level": 5}}]  # 排他要素更新

输入：给我检索治安管理条例，只要我省级文件，不要市级文件
已知要素: []
输出：[{{"facts": "用户关注:省级文件", "level": 1}}, {{"facts": "用户排除:市级文件", "level": 1}}]  # 要素新增

输入：昨天下午我们讨论了新项目。
已知要素：[]
输出：[]  # 与记忆范围无关

输入：帮我记住我最喜欢的电影是《盗梦空间》和《星际穿越》。
已知要素：[]
输出：[{{"facts": "最喜欢的电影:《盗梦空间》和《星际穿越》", "level": 5}}]

以 JSON 格式返回上述事实和偏好。

请记住以下几点：
- 今天的日期是 {datetime.datetime.now().strftime("%Y-%m-%d")}。
- 不要返回上面提供的自定义示例提示中的任何内容。
- 除非用户要求，否则只记录在工作和研究中对检索和生成有帮助的内容，例如饮食喜好、用户身高体重等信息与工作无关就不需要记录。
- 如果您在下面的对话中找不到任何相关信息，必须返回空列表。
- 仅根据用户和助手消息创建要素。不要从系统消息中选取任何内容。
- 直接返回json格式，不要返回其他信息

/no_think"""