#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
import asyncio
import psutil
import shutil
import uuid
import tempfile
from pathlib import Path

from config import LIBREOFFICE_TIMEOUT
from common.logger import logger


class ConvertLocalTask:
    """
    LibreOffice文件格式转换器
    使用独立的临时用户配置文件避免并发冲突
    支持异步并发转换，最多同时执行4个转换任务
    """

    def __init__(self):
        # 实例级别的信号量，限制最多同时执行4个转换
        self._conversion_semaphore = asyncio.Semaphore(4)

    async def libreoffice_convert(self, input_path: Path, output_dir: Path, exporter: str, log_prefix: str = "") -> Path:
        # 使用信号量控制并发数量
        async with self._conversion_semaphore:
            # 为每个转换创建唯一的临时用户配置文件目录
            temp_profile_id = uuid.uuid4().hex
            # 使用跨平台的临时目录
            temp_profile_dir = Path(tempfile.gettempdir()) / f"libreoffice_profile_{temp_profile_id}"

            try:
                # 确保临时目录和输出目录存在
                temp_profile_dir.mkdir(parents=True, exist_ok=True)
                output_dir.mkdir(parents=True, exist_ok=True)

                # 验证输入文件存在
                if not input_path.exists():
                    raise FileNotFoundError(f"{log_prefix}Input file not found: {input_path}")

                logger.info(f"{log_prefix}开始转换文件: {input_path} -> {output_dir} (格式: {exporter}) 输入文件大小: {input_path.stat().st_size} bytes")

                command = [
                    "libreoffice",
                    f"-env:UserInstallation=file://{temp_profile_dir}",
                    "--headless",
                    "--nolockcheck",  # 禁用文件锁检查
                    "--nologo",      # 不显示启动画面
                    "--norestore",   # 不恢复上次会话
                    "--convert-to", exporter,
                    "--outdir", output_dir.as_posix(),
                    input_path.as_posix()
                ]

                logger.info(f"{log_prefix}调用libreoffice (profile: {temp_profile_id}): {' '.join(command)}")

                # 异步创建子进程
                process = await asyncio.create_subprocess_shell(" ".join(command))

                try:
                    # 使用asyncio.wait_for处理超时，不阻塞事件循环
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(),
                        timeout=LIBREOFFICE_TIMEOUT
                    )

                    # 记录LibreOffice的输出信息
                    if stdout:
                        stdout_msg = stdout.decode('utf-8', errors='ignore')
                        logger.info(f"{log_prefix}libreoffice stdout: {stdout_msg}")

                    if stderr:
                        stderr_msg = stderr.decode('utf-8', errors='ignore')
                        logger.info(f"{log_prefix}libreoffice stderr: {stderr_msg}")

                    if process.returncode != 0:
                        error_msg = stderr.decode('utf-8', errors='ignore') if stderr else "Unknown error"
                        logger.error(f"{log_prefix}libreoffice 转换失败 (返回码: {process.returncode}): {error_msg}")
                        raise RuntimeError(f"LibreOffice conversion failed: {error_msg}")

                except asyncio.TimeoutError:
                    logger.error(f"{log_prefix}libreoffice 转换超时 (profile: {temp_profile_id})")

                    # 异步强制终止进程及其子进程
                    await self._kill_process_tree_async(process.pid, log_prefix, temp_profile_id)
                    raise

            finally:
                # 异步清理临时用户配置文件目录
                await self._cleanup_temp_profile_async(temp_profile_dir, log_prefix, temp_profile_id)

            suffix = exporter.split(":")[0] if ":" in exporter else exporter
            expected_output_path = output_dir / (Path(input_path).stem + f".{suffix}")

            # 验证输出文件是否真的被创建
            if not expected_output_path.exists():
                logger.error(f"{log_prefix}转换失败: 输出文件不存在 {expected_output_path}")
                raise FileNotFoundError(f"Converted file not found: {expected_output_path}")

            logger.info(f"{log_prefix}转换完成，输出文件: {expected_output_path} (大小: {expected_output_path.stat().st_size} bytes)")
            return expected_output_path

    async def _kill_process_tree_async(self, pid: int, log_prefix: str, profile_id: str):
        """
        异步强制终止LibreOffice进程及其所有子进程
        """
        try:
            parent = psutil.Process(pid)
            # 获取所有子进程
            children = parent.children(recursive=True)

            # 先尝试优雅地终止子进程
            for child in children:
                try:
                    logger.info(f"{log_prefix}转换超时,尝试终止子进程 [{child.pid}] (profile: {profile_id})")
                    child.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 等待一小段时间让进程优雅退出
            await asyncio.sleep(1)

            # 强制杀死仍然存在的子进程
            for child in children:
                try:
                    if child.is_running():
                        logger.info(f"{log_prefix}强制杀死子进程 [{child.pid}] (profile: {profile_id})")
                        child.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 最后终止父进程
            try:
                logger.info(f"{log_prefix}转换超时,尝试终止父进程 [{parent.pid}] (profile: {profile_id})")
                parent.terminate()
                await asyncio.sleep(1)
                if parent.is_running():
                    logger.info(f"{log_prefix}强制杀死父进程 [{parent.pid}] (profile: {profile_id})")
                    parent.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        except psutil.NoSuchProcess:
            logger.info(f"{log_prefix}进程 [{pid}] 已不存在 (profile: {profile_id})")
        except Exception as e:
            logger.error(f"{log_prefix}清理进程时发生错误 (profile: {profile_id}): {e}")

    async def _cleanup_temp_profile_async(self, temp_profile_dir: Path, log_prefix: str, profile_id: str):
        """
        异步清理临时用户配置文件目录
        """
        try:
            # 在线程池中执行文件系统操作，避免阻塞事件循环
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._cleanup_temp_profile_sync, str(temp_profile_dir), log_prefix, profile_id)
        except Exception as e:
            logger.warning(f"{log_prefix}异步清理临时配置文件目录失败 (profile: {profile_id}): {e}")

    def _cleanup_temp_profile_sync(self, temp_profile_dir: str, log_prefix: str, profile_id: str):
        """
        同步清理临时用户配置文件目录（在线程池中执行）
        """
        try:
            if os.path.exists(temp_profile_dir):
                shutil.rmtree(temp_profile_dir, ignore_errors=True)
                logger.debug(f"{log_prefix}已清理临时配置文件目录 (profile: {profile_id})")
        except Exception as e:
            logger.warning(f"{log_prefix}清理临时配置文件目录失败 (profile: {profile_id}): {e}")


LocalConverter = ConvertLocalTask()
