#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from typing import List
from enum import Str<PERSON><PERSON>

import httpx
from lxml import html as lxmlhtml

from config import MINERU_PARSER_URL
from common.logger import logger, async_time_cost
from common import g
from controller.parser.base import ParserBase, ReferenceType
from controller.parser.chunker.mineru_pdf_chunker import MineUPDFChunker
from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
from controller.engine import EmbeddingEngine
from controller.repository import Doc, DocStatus, SupportFormat
from controller.system_config import Strategy


class MinerUType(StrEnum):
    title = "title"
    text = "text"
    list = "list"
    image = "image"
    table = "table"
    formula = "formula"
    index = "index"
    interline_equation = "interline_equation"


class MinerUParser(ParserBase):
    def __init__(self,
                 doc_id: int,
                 doc: dict = None):
        super().__init__(
            doc_id=doc_id,
            chunker=MineUPDFChunker(tokenizer=BGE_M3_TOKENIZER),
            reference_type=ReferenceType.pdf
        )
        self.parse_timeout = 3600
        self.parsed_blocks: List[dict] = []

        # strategy解析配置,从load_strategy读取
        self.embedding_engine: EmbeddingEngine | None = None
        self.embedding_batch_size: int = 0
        self.do_table_structure: bool = True
        self.do_formula_enrichment: bool = True

        self.doc = doc

    async def parsing(self):
        await self.load_tenant_strategy()

        await Doc.update(doc_id=self.doc_id, status=DocStatus.parsing)
        await g.session.commit()

        suffix = self.local_path.suffix

        if suffix in SupportFormat.__members__.values() and suffix != SupportFormat.pdf.value:
            # 使用LibreOffice进行预转换
            await self.libreoffice_convert(target_format="pdf")
            await self.upload(
                local_path=self.local_path,
                s3_path=self.s3_path.with_suffix(".pdf"))

        file_result, images = await self.mineru_source_request()
        self.parsed_blocks = self.convert_to_chunks(file_result=file_result, images=images)

        html_body = "\n\n".join([b["html"] for b in self.parsed_blocks])
        self.html = f"<!DOCTYPE html><html><body>{html_body}</body></html>"

        logger.info(f"{self.log_prefix}parsing阶段完成")

    async def chunking(self):
        """
        pdf解析的文档有其特殊性,包括:
            1. 包含溯源角标
            2. 段落通常不会很长
            3. 块结果更精准等
        出于以上考虑,特殊化切分策略,不对长段落切分，不对表格进行切分,

        """
        self.chunks = self.chunker.chunk(parsed_blocks=self.parsed_blocks)
        logger.info(f"{self.log_prefix}chunking阶段完成")

    async def load_tenant_strategy(self):
        parser_strategy = await Strategy.get_parser_strategy(tenant_id=self.tenant_id)
        self.embedding_engine = parser_strategy["embedding_engine"]
        self.embedding_batch_size = parser_strategy["embedding_batch_size"]
        self.do_table_structure = parser_strategy["do_table_structure"]
        self.do_formula_enrichment = parser_strategy["do_formula_enrichment"]

    @async_time_cost()
    async def mineru_source_request(self) -> tuple[dict[str, dict], dict[str, dict]]:
        logger.info(f"{self.log_prefix}请求解析")

        async with httpx.AsyncClient(timeout=self.parse_timeout) as client:
            try:
                response = await self._mineru_request()
                response.raise_for_status()
            except Exception as err:
                logger.error(f"{self.log_prefix}解析失败: {err}")
                raise err

        resp_json = response.json()
        file_result = resp_json["results"][self.local_path.stem]
        images = file_result["images"]

        return file_result, images

    def convert_to_chunks(self, file_result: dict, images: dict):
        doc_blocks = []
        middle_json = json.loads(file_result["middle_json"])
        pages_size = {p["page_idx"] + 1: p["page_size"] for p in middle_json["pdf_info"]}
        for page in middle_json["pdf_info"]:
            for block in page["para_blocks"]:
                type_ = block["type"]
                p = page["page_idx"] + 1
                bboxes = self._build_page_box(p=p, pages_size=pages_size, block=block)

                if type_ == MinerUType.image:
                    plain_text = ""
                    image_caption = []
                    img_tags = []
                    for b in block["blocks"]:
                        # 图片本体,构造html标签
                        if b["type"] == "image_body":
                            for line in b["lines"]:
                                for span in line["spans"]:
                                    if span.get("image_path"):
                                        img_tags.append(f'<img src="{images[span["image_path"]]}">')
                        # 图片描述
                        if b["type"] == "image_caption":
                            text = self._storage_text(b)
                            plain_text += f"{text} "
                            image_caption.append(f"<span>{text}</span>")
                    # 最后,如有多个image_caption,组合为figcaption
                    html_content = (f"<figcaption>"
                                    f"{''.join(img_tags)}"
                                    f"{'<br>'.join(image_caption)}"
                                    f"</figcaption>")

                elif type_ == MinerUType.table:
                    plain_text = ""
                    table_caption = []
                    table_body = ""
                    for b in block["blocks"]:
                        # 表格本体,构造html标签
                        if b["type"] == "table_body":
                            if table_body:
                                print("MinerU解析同para下出现多个表格")
                            for line in b["lines"]:
                                for span in line["spans"]:
                                    table_body = span["html"][19:-22]
                                    plain_text = lxmlhtml.fromstring(table_body).text_content()

                        # 表格描述
                        if b["type"] == "table_caption":
                            text = self._storage_text(b)
                            plain_text += f"{text} "
                            table_caption.append(text)

                    html_content = "<table>" + "\n".join(table_caption) + table_body + "</table>"

                elif type_ == MinerUType.title:
                    text = self._storage_text(block)
                    plain_text = text
                    if len(doc_blocks) < 2:
                        html_content = f"<h1>{text}</h1>"
                    else:
                        html_content = f"<h2>{text}</h2>"

                elif type_ == MinerUType.list:
                    text = self._storage_text(block)
                    plain_text = text.replace(" \n ", " ")
                    li_tags = [f"<li>{text}</li>" for text in text.split(" \n ")]
                    html_content = f"<p>{''.join(li_tags)}</p>"

                elif type_ == MinerUType.index:
                    text = self._storage_text(block, hard_line_white=True)
                    plain_text = text
                    html_content = f"<ol>{text}</ol>"

                elif type_ == MinerUType.text:
                    text = self._storage_text(block)
                    plain_text = text
                    html_content = f"<p>{text}</p>"

                elif type_ == MinerUType.interline_equation:
                    equations = []
                    html_content = ""
                    for line in block["lines"]:
                        for span in line["spans"]:
                            equations.append(span["content"])
                            html_content += f'<p><img src="{images[span["image_path"]]}"></p>'
                    plain_text = " \n ".join([f"$${eq}$$" for eq in equations])

                else:
                    logger.warning(self.log_prefix + f"有未知的MinerU paragraph <{type_}>")
                    continue

                if not bboxes:
                    continue

                doc_blocks.append({
                    "html": html_content,
                    "plain_text": plain_text,
                    "bboxes": bboxes
                })

        return doc_blocks

    @async_time_cost()
    async def _mineru_request(self):
        async with httpx.AsyncClient(timeout=self.parse_timeout) as client:
            parameters = {
                "return_middle_json": True,
                "return_model_output": False,
                "return_md": False,
                "return_images": True,
                "parse_method": "auto",
                "backend": "pipeline",
                "table_enable": self.do_table_structure,
                "return_content_list": False,
                "formula_enable": self.do_formula_enrichment,
            }

            return await client.post(
                url=MINERU_PARSER_URL,
                data=parameters,
                files={"files": (self.local_path.name, open(self.local_path.as_posix(), mode="rb"))})

    @staticmethod
    def _storage_text(block: dict, full_line_radio: float = 0.7, hard_line_white: bool = False):
        text = ""
        block_bbox = block["bbox"]
        for i, line in enumerate(block["lines"]):
            spans = []
            for span in line["spans"]:
                if span.get("content"):
                    spans.append(span["content"])
            text += " ".join(spans)

            if line.get("is_list_end_line"):
                text += " \n "  # 特殊标记
            if (
                    text  # 已有文本
                    and i != 0  # 索引不是第一位
                    and text[-1] != " "
                    and (
                            block["lines"][i-1]["bbox"][2] < block_bbox[2] * full_line_radio  # 上一行x1不足满行 full_line_radio%
                            or (
                                    text[-1].isalpha() and ord(text[-1]) < 128  # 英文字母
                                    or text[-1].isdigit()  # 或数字
                            )
                    )):
                # 极大概率是换行, 增加换行符
                text += " "
            elif hard_line_white:
                text += " "
            else:
                pass

        return text.strip()

    @staticmethod
    def _build_page_box(p: int, pages_size: dict, block: dict):
        # 2.1.1版本,para_blocks的bbox和其下lines.bbox坐标不对等.
        # 为了处理跨页问题,以lines.bbox为准
        boxes = []
        # cross_page标记只能管翻过一页,但是不管跨两页.
        # 使用标记强制翻第一页,根据bbox有选择的翻第二页
        cross_page_flag = False

        if block.get("blocks"):
            block.setdefault("lines", [])
            block["lines"].extend([line for b in block["blocks"] for line in b["lines"]])

        for line in block["lines"]:
            if not boxes:
                boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
                continue

            # layout判断跨超过一页的情况.需要cross_page不在本次出现,并且bbox有大幅变动
            if (cross_page_flag is True
                    and abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30  # y0小于上个bbox y1 20个像素以上
                    and line["bbox"][0] < boxes[-1]["bbox"][2]  # x0不大于上个bbox的x1
            ):
                p += 1
                boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
                continue

            # 首次出现cross_page 强制翻页
            if line["spans"] and line["spans"][0].get("cross_page") and cross_page_flag is False:
                cross_page_flag = True
                p += 1
                boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
                continue

            # 块产生较大变动,重新分块
            if line["bbox"][0] > boxes[-1]["bbox"][2] or abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30:
                boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
                continue

            # 未检测到的其他情况,扩展bbox
            boxes[-1]["bbox"][0] = min(boxes[-1]["bbox"][0], line["bbox"][0])
            boxes[-1]["bbox"][1] = min(boxes[-1]["bbox"][1], line["bbox"][1])
            boxes[-1]["bbox"][2] = max(boxes[-1]["bbox"][2], line["bbox"][2])
            boxes[-1]["bbox"][3] = max(boxes[-1]["bbox"][3], line["bbox"][3])

        return boxes

# if __name__ == '__main__':
#     from controller.parser.chunker import HtmlChunker, BGE_M3_TOKENIZER
#     from engine.rdb import session_maker, g
#     import asyncio
#     g.session = session_maker()
#     asyncio.run(DoclingParser(doc_id=4796, chunker=HtmlChunker(tokenizer=BGE_M3_TOKENIZER)).exec())