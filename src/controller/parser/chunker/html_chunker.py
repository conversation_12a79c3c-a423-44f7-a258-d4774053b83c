#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

from lxml import etree, html as lxmlhtml
from transformers import AutoTokenizer

from common.logger import logger
from controller.parser.chunker import Chunker, ChunkNode


class HtmlChunker(Chunker):
    def __init__(self,
                 tokenizer: AutoTokenizer,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):
        super().__init__(max_tokens=max_tokens, max_tolerance_tokens=max_tolerance_tokens, tokenizer=tokenizer)

    def chunk(self, html_content: str):
        self.parsing(html_content=html_content)
        self.add_splittable_symbol()
        self.assign_chunk_offsets()

        return self.chunks

    def parsing(self, html_content):
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        if tree is None:
            raise ValueError("html_content is not a valid html string")
        root = tree.getroottree()  # 用于寻找xpath

        body_tag = tree.xpath('//body')[0]
        context = etree.iterwalk(body_tag, events=("start",))

        for action, element in context:
            tag = element.tag.lower()
            element_text = element.text_content()

            # 过滤空文本节点
            if not element_text.strip():
                continue

            # 无视内部子节点,直接可从以下节点提取所有文本信息的
            # 每个节点提取
            if (tag in ("table", "p", "a", "span", "li", "i", "strong", "h1", "h2", "h3", "h4", "h5", "h6", "em", "b",
                        "dt", "code")
                    # 某些惯用父级的标签,只有在无子节点时才进行处理
                    or (len(element) == 0 and tag in ("div", "center"))
                    # 部分可能对正文无关的标签,只在字数较长时才进行处理
                    or (tag in ("button", "option", "sub") and len(element_text) > 5)):
                fragments = [
                    self.replace_one_white_pattern.sub("", text.strip())
                    for text in element.xpath('.//text()[normalize-space() != ""]')
                ]
                plain_content = " ".join(fragments).replace("\n", "")

                # 替换element中的"None"为空格
                element = self._remove_style_attributes(element)
                html_content = lxmlhtml.tostring(element, encoding="unicode", pretty_print=False)
                html_content = self.remove_html_blank.sub("", html_content)

                xpath = root.getpath(element)
                token_counts = len(self.tokenizer.encode(text=html_content))
                if token_counts <= self.max_tolerance_tokens:
                    self.add_node(
                        tag=tag, xpath=xpath, html_content=html_content, plain_content=plain_content,
                        token_counts=token_counts)
                else:
                    logger.warning(
                        f"标签[{tag}] tokens [{token_counts}] 已超过最大容忍值 [{self.max_tolerance_tokens}] 按照标签进行细粒度处理")
                    # 当table的tokens超过长度时，跳过并记录th列头，并将每一行的正文的html和列头拼凑
                    if tag == "table":
                        # 对于超长表格,单独按行处理
                        self.chunk_large_table_tag(element=element, root=root)
                    else:
                        # 对于其他超长标签，使用文本切分方法
                        self.chunk_large_text_tag(element=element, root=root)
                # 子节点全部从内存中删除
                for child in element:
                    element.remove(child)
                continue

            if len(element) == 0:
                # 这里button、sub、option
                if tag in ("script", "style", "button", "option", "sub"):
                    pass

                else:
                    # 用于检查逻辑遗漏
                    logger.error(f"有未命中的节点"
                                 f"Tag: {tag}, "
                                 f"Text: {element.text_content()} "
                                 f"Xpath: {root.getpath(element)} "
                                 f'HTML: {lxmlhtml.tostring(element, encoding="unicode", pretty_print=True)} ')


    def _remove_style_attributes(self, element):
        """递归移除元素及其子元素的所有样式相关属性"""
        # 定义样式相关属性列表
        style_attrs = [
            "style", "class", "id", "align", "width", "height", "bgcolor", "color", "font", "margin", "padding",
            "border", "cellpadding", "cellspacing", "valign", "background", "face", "size", "text-align"
        ]

        # 从当前元素中移除样式属性
        for attr in style_attrs:
            if attr in element.attrib:
                del element.attrib[attr]

        # 递归处理所有子元素
        for child in element:
            self._remove_style_attributes(child)

        return element

if __name__ == '__main__':
    from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
    chunker = HtmlChunker(BGE_M3_TOKENIZER)
    chunker.chunk(html_content="""<!DOCTYPE html>

<html>
<head>
	
	<meta http-equiv="content-type" content="text/html; charset=utf-8"/>
	<title></title>
	<meta name="generator" content="LibreOffice 7.4.7.2 (Linux)"/>
	<meta name="author" content="DingTalk"/>
	<meta name="created" content="2006-09-16T00:00:00"/>
	<meta name="changedby" content="Microsoft Office User"/>
	<meta name="changed" content="2023-08-31T07:26:01"/>
	<meta name="AppVersion" content="16.0300"/>
	
	<style type="text/css">
		body,div,table,thead,tbody,tfoot,tr,th,td,p { font-family:"等线"; font-size:small }
		a.comment-indicator:hover + comment { background:#ffd; position:absolute; display:block; border:1px solid black; padding:0.5em;  } 
		a.comment-indicator { background:red; display:inline-block; border:1px solid black; width:0.5em; height:0.5em;  } 
		comment { display:none;  } 
	</style>
	
</head>

<body>
<hr>
	<p><center>
		<h1>Overview</h1>
		<A HREF="#table0">标准产品报价（永久授权）</A><br>
		<A HREF="#table1">标准产品报价（年费制）</A><br>
		<A HREF="#table2">增值服务</A><br>
		
	</center></p>
<hr>
<A NAME="table0"><h1>Sheet 1: <em>标准产品报价（永久授权）</em></h1></A>
<table cellspacing="0" border="0">
	<colgroup width="180"></colgroup>
	<colgroup width="209"></colgroup>
	<colgroup width="539"></colgroup>
	<colgroup width="131"></colgroup>
	<colgroup width="117"></colgroup>
	<colgroup width="148"></colgroup>
	<colgroup width="99"></colgroup>
	<colgroup width="183"></colgroup>
	<tr>
		<td style="border-top: 1px solid #000000" height="4" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="left" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="left" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td colspan=2 rowspan=3 height="90" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_b4e0157c1e7c863a.png" width=322 height=63 hspace=33 vspace=14>
		</font></b></td>
		<td colspan=3 rowspan=3 align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_af8480bcd7f2a3e0.png" width=607 height=67 hspace=91 vspace=12>
		</font></b></td>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">客户名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=2 align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">达观数据有限公司</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位联系地址</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=2 align="left" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价单位联系人</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价日期</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=2 align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价有效期至</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="29" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">RPA软件产品报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="25" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">产品名称</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类型</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=14 height="364" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-机器人<br>Windows版</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务拉取</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=14 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">必选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdval="45000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">45,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务运行</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务异常截图</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人远程控制</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务拉取</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务运行</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务异常截图</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人远程控制</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 height="338" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-开发平台<br>Windows版</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000"><br>设计器浮动授权许可，授权许可不绑定机器，只能配合控制中心使用</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化流程开发调试</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle sdval="60000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">60,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">元素捕获器及元素库</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">画中画模式</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">代码流程开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">编排流程开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化流程库开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">代码流程库开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">500+标准可视化控件</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化控件设计</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化控件版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">工程依赖库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=48 height="1256" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-控制中心</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量上限为10个，仅支持单租户管理。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量上限为50个，支持多租户管理。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="300000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">300,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量不限，仅支持多租户管理。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="450000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">450,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="92" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-开发平台<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10桌面版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=4 height="184" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-控制中心<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10服务器版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据库适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配人大金仓、达梦数据库（MySQL模式）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">容器中间件适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">华为CCE云、腾讯灵雀云</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="92" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-机器人<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10桌面版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-left: 1px solid #000000; border-right: 1px solid #000000" height="46" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-数字员工平台</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">授权许可绑定机器</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">包含数字员工商城及运维平台、数字员工监控平台、门户展示及管理平台等功能。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="500000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">500,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="25" align="center" valign=middle bgcolor="#D6DCE5"><b><font face="Noto Sans" size=2 color="#000000">RPA小计</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=bottom bgcolor="#D6DCE5" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="58" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">业务规则包报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="33" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=5 height="250" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">财税审核规则包</font></b></td>
		<td style="border-top: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">预付款审核规则</font></td>
		<td align="left" valign=bottom><font face="Noto Sans" size=2 color="#000000">80余条审核规则，针对预付款项的必要性、合同预付约定、付款备注、付款方式及付款计划进行审核</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日常费用报销审核规则</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">260余条审核规则，针对非生产经营活动产生的管理费用、研发费用进行审核</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="40000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">40,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">差旅费报销审核规则</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">110余条审核规则，针对因公出差产生的公共交通费、自驾补贴、出差补助、住宿、会务等费用进行审核</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">采购结算审核规则</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">60余条审核规则，针对非集采类的生产性原料物资采购进行审核</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">对公支付费用审核规则</font></td>
		<td style="border-top: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">190余条审核规则，针对辅助生产、日常运营、对外投资等情况下产生的对外支付费用进行审核</font></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="40000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">40,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=4 height="405" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">网银流程包</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">银企对账流程包</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">30+家银行的银企对账流程包，如广发银行、海南银行、南京银行、宁波通商银行、农业银行、平安银行、青岛银行、浦发银行、兴业银行、浙商银行、中国进出口银行、中国银行等</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">银行流水下载流程包</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">30+家银行的银行流水下载流程包，如广发银行、海南银行、南京银行、宁波通商银行、农业银行、平安银行、青岛银行、浦发银行、兴业银行、浙商银行、中国进出口银行、中国银行等</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=bottom><font face="Noto Sans" size=2 color="#000000">银行回单查询下载流程包</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">30+家银行的银行回单查询下载流程包，，如广发银行、海南银行、南京银行、宁波通商银行、农业银行、平安银行、青岛银行、浦发银行、兴业银行、浙商银行、中国进出口银行、中国银行等</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">银行U盾统一管理流程包</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">30+家银行的U盾统一管理流程包，，如广发银行、海南银行、南京银行、宁波通商银行、农业银行、平安银行、青岛银行、浦发银行、兴业银行、浙商银行、中国进出口银行、中国银行等</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="32" align="center" valign=middle bgcolor="#D6DCE5"><b><font face="Noto Sans" size=2 color="#000000">业务规则包小计</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=bottom bgcolor="#D6DCE5" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="22" align="center" valign=middle bgcolor="#D6DCE5"><b><font face="Noto Sans" size=2 color="#000000">折扣</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE5" sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">5-8折</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="22" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">报价总计</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
</table>
<!-- ************************************************************************** -->
<hr>
<A NAME="table1"><h1>Sheet 2: <em>标准产品报价（年费制）</em></h1></A>
<table cellspacing="0" border="0">
	<colgroup width="209"></colgroup>
	<colgroup width="180"></colgroup>
	<colgroup width="559"></colgroup>
	<colgroup width="131"></colgroup>
	<colgroup width="117"></colgroup>
	<colgroup width="148"></colgroup>
	<colgroup width="99"></colgroup>
	<colgroup width="183"></colgroup>
	<tr>
		<td style="border-top: 1px solid #000000" height="4" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="left" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="left" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td style="border-top: 1px solid #000000" align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=4 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td colspan=2 rowspan=3 height="90" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_b4e0157c1e7c863a.png" width=322 height=63 hspace=33 vspace=14>
		</font></b></td>
		<td colspan=3 rowspan=3 align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_af8480bcd7f2a3e0.png" width=607 height=67 hspace=100 vspace=12>
		</font></b></td>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">客户名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">达观数据有限公司</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位联系地址</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价单位联系人</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价日期</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价有效期至</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="29" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">RPA软件产品报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000" height="25" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">产品名称</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">功能</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元/年）</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=14 height="364" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-机器人<br>Windows版</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">执行器浮动授权许可，授权许可不绑定机器，只能配合控制中心使用。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务拉取</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=14 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">必选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdval="15000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">15,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务运行</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务异常截图</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人远程控制</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务拉取</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdval="10000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">10,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=7 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务运行</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程任务异常截图</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人远程控制</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" rowspan=13 height="338" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-开发平台<br>Windows版</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000"><br>设计器浮动授权许可，授权许可不绑定机器，只能配合控制中心使用</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化流程开发调试</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle sdval="20000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">20,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=13 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">元素捕获器及元素库</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">画中画模式</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">代码流程开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">编排流程开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化流程库开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">代码流程库开发调试</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">500+标准可视化控件</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化控件设计</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">可视化控件版本管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">工程依赖库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=48 height="1256" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">基础模块-控制中心</font></b></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量上限为10个，仅支持单租户管理。</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="50000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">50,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量上限为50个，支持多租户管理。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">控制器固定授权许可，授权绑定机器，机器人管理数量不限，仅支持多租户管理。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据、流程及机器人监控大屏</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=16 align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">任务管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">作业管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">流程库管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">控件管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">机器人组管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">组织架构管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">用户管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">角色管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">审计日志</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">需求管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">许可管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">日历管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通知管理</font></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="92" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-开发平台<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10桌面版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=4 height="184" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-控制中心<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10服务器版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">数据库适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配人大金仓、达梦数据库（MySQL模式）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">容器中间件适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">华为CCE云、腾讯灵雀云</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="92" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-机器人<br>信创环境适配</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配X86架构芯片（海光、兆芯）和ARM架构芯片（飞腾、鲲鹏）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配统信UOS V20和麒麟KylinOS V10桌面版</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">个</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="8000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">8,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-left: 1px solid #000000; border-right: 1px solid #000000" height="46" align="center" valign=middle><b><font face="Noto Sans" size=2 color="#000000">高级模块-数字员工平台</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">授权许可绑定机器</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">包含数字员工商城及运维平台、数字员工监控平台、门户展示及管理平台等功能。</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle sdval="100000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">100,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="25" align="center" valign=middle bgcolor="#D6DCE5"><b><font face="Noto Sans" size=2 color="#000000">RPA小计</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=bottom bgcolor="#D6DCE5" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="22" align="center" valign=middle bgcolor="#D6DCE5"><b><font face="Noto Sans" size=2 color="#000000">折扣</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE5" sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">5-8折</font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=7 height="22" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">报价总计</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
</table>
<!-- ************************************************************************** -->
<hr>
<A NAME="table2"><h1>Sheet 3: <em>增值服务</em></h1></A>
<table cellspacing="0" border="0">
	<colgroup width="96"></colgroup>
	<colgroup width="277"></colgroup>
	<colgroup width="560"></colgroup>
	<colgroup width="116"></colgroup>
	<colgroup span="4" width="96"></colgroup>
	<tr>
		<td colspan=2 rowspan=3 height="90" align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_b4e0157c1e7c863a.png" width=322 height=63 hspace=26 vspace=14>
		</font></b></td>
		<td colspan=3 rowspan=3 align="center" valign=middle bgcolor="#0066D1"><b><font face="微软雅黑" size=4 color="#000000"><br><img src="1_html_af8480bcd7f2a3e0.png" width=607 height=67 hspace=83 vspace=12>
		</font></b></td>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td align="center" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="center" valign=middle bgcolor="#0066D1"><font face="微软雅黑" size=6 color="#FFFFFF"><br></font></td>
		<td align="right" valign=middle bgcolor="#0066D1" sdnum="1033;0;#,##0_ "><b><font face="微软雅黑" size=2 color="#FFFFFF"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">客户名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位名称</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">达观数据有限公司</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">报价单位联系地址</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价单位联系人</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="30" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价日期</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle bgcolor="#FFFFFF"><font face="Noto Sans" size=2 color="#000000">报价有效期至</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#FFFFFF" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="30" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">流程开发报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="25" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=6 height="180" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">流程开发</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">需求分析师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">提供业务流程场景调研、自动化需求分析、需求文档撰写等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">初级实施工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">1年以下流程开发经验的实施工程师进行RPA流程开发</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">中级实施工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">2~3年流程开发经验的实施工程师进行RPA流程开发</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="4000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">4,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">高级实施工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">3年以上流程开发经验的实施工程师进行RPA流程开发</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">测试工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">结合业务自动化需求，对RPA进行测试和质量保证</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="4000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">4,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项目经理</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">为RPA流程开发项目提供项目管理服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="34" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">流程开发小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="30" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">定制化功能报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="25" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=5 height="150" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">定制化功能</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">产品经理</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对定制化功能提供需求调研、原型设计等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">UI/UE 设计</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对定制化功能提供交互设计、页面美工等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">前端开发工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对定制化功能提供前端功能开发等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">后端开发工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对定制化功能提供后端API、数据库、业务功能开发等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">测试工程师</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对定制化功能提供系统测试等服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="4000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">4,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="34" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">定制化功能小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">信创适配报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">小计（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU芯片</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">CPU适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配国产CPU芯片（已适配型号以外的环境）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">操作系统适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配国产操作操作系统（已适配版本以外的环境）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">中间件</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">中间件适配</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配国产中间件（已适配产品以外的环境）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">容器管理平台</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">容器管理平台适配</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配信创容器管理平台（已适配版本以外的环境）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">数据库</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">数据库适配</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">适配国产数据库（已适配版本以外的环境）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" height="22" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">国密算法</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">国密算法改造</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">国密算法改造及国密算法加密套件应用</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="5000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">5,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">信创适配小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">源码报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">小计（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" rowspan=3 height="66" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">源码交付</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">定制化功能源码</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">交付项目定制化功能源码</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">按项目</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000">/</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#FF0000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">全部产品源码</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">交付项目产品全部源码</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">按项目</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="right" valign=middle><font face="Noto Sans" size=2 color="#000000">面议</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#FF0000"><br></font></td>
	</tr>
	<tr>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">特殊语言适配</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">达观RPA产品由Go、JS、Python等开发，如需要改为客户的其他代码语言C /JAVA</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">按项目</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000" align="right" valign=middle><font face="Noto Sans" size=2 color="#000000">面议</font></td>
		<td style="border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#FF0000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">源码报价小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">部署报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">小计（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=5 height="110" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">部署报价</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">裸机部署</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">在裸机上直接安装和配置软件和操作系统</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">/</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">虚拟机部署</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">在虚拟化平台安装安装和配置软件和操作系统</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">云部署</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">将应用程序、服务或系统部署到云计算平台上运行的过程</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">混合部署</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">将应用程序、服务或系统部署到混合物理机和云计算平台上运行</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="30000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">30,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">特殊部署</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">将应用程序、服务或系统部署到第三方容器</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">套</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="50000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">50,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">部署小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">运维服务报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">小计（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="67" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">普通维保</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">普通维保</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">通过远程、现场等方式保证线上服务稳定性</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">年</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">项目价格*15%</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">环境迁移</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">项目上线后部署机器迁移</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=2 height="67" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">产品升级</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">产品功能升级</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">本地部署产品功能升级</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">节点</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;#,##0_ "><font face="Noto Sans" size=2 color="#000000">产品价格的*40%</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">流程升级维护</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">本地已实施流程升级维护</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人天</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">运维服务小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA" sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#FF0000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">科创增值报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=5 height="156" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">科创增值</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">专利申请</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于合作的产品与项目建设内容，联合撰写中国专利申请材料和共同申请专利</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">课题申请</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于合作的产品与项目建设内容，联合撰写申请材料申请政府、监管、专业协会等类型课题</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">奖项申请</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于合作产品与项目建设内容，联合撰写材料申请国家、地方政府、行业协会、集团内部等各类奖项材料</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">论文撰写（不包括核心期刊一作）</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于合作产品与项目建设内容，联合撰写对应领域论文相关材料</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="150000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">150,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">软著申请</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于合作产品与项目建设内容，联合撰写联合研发产品的软件著作权申请材料</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">项</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="50000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">50,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">奖项申报小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">技术咨询报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=3 height="66" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">技术咨询</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">产品培训</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">进行产品使用方法、产品的业务等培训</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">流程开发培训</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">进行RPA流程的需求分析、流程开发、流程测试等培训</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">运维及其他技术培训</font></td>
		<td style="border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">包括docker、k8s、中间件队列等运维及其他技术培训</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">技术咨询小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000" colspan=8 height="22" align="center" valign=middle bgcolor="#0066D1"><b><font face="Noto Sans" size=2 color="#FFFFFF">战略咨询报价</font></b></td>
		</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" height="21" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">项目</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">类别</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">描述</font></b></td>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">必选/可选</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">单位</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4" sdnum="1033;0;#,##0_ "><b><font face="Noto Sans" size=2 color="#000000">单价（元）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">数量</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#D6DCE4"><b><font face="Noto Sans" size=2 color="#000000">总价（元）</font></b></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" rowspan=3 height="112" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">战略咨询</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">管理战略咨询</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">基于行业领先的超自动化战略规划经验，提供集团级别构建基于超自动化的数字化智能化战略转型咨询和规划服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">IT架构咨询</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">提供构建集团级超自动化应用建设的技术架构咨询和规划服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">业务数智化转型咨询</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="left" valign=middle><font face="Noto Sans" size=2 color="#000000">针对具体业务流程场景和痛点，结合行业先进经验，提供基于超自动化技术实现业务数智化转型的咨询和规划服务</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">可选</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="Noto Sans" size=2 color="#000000">人/小时</font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdval="3000" sdnum="1033;0;#,##0_ "><font face="微软雅黑" size=2 color="#000000">3,000 </font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle><font face="微软雅黑" size=2 color="#000000"><br></font></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle sdnum="1033;0;&quot;￥&quot;#,##0.00;&quot;￥&quot;-#,##0.00"><font face="微软雅黑" size=2 color="#000000"><br></font></td>
	</tr>
	<tr>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" colspan=6 height="21" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="Noto Sans" size=2 color="#000000">战略咨询小计（含税）</font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="center" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
		<td style="border-top: 1px solid #000000; border-bottom: 1px solid #000000; border-left: 1px solid #000000; border-right: 1px solid #000000" align="right" valign=middle bgcolor="#ADB9CA"><b><font face="微软雅黑" size=2 color="#000000"><br></font></b></td>
	</tr>
</table>
<!-- ************************************************************************** -->
</body>

</html>
""")

    for chunk in chunker.chunks:
        print(chunk)
