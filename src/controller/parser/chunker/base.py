#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
from enum import Enum
from typing import Optional, List

from pydantic import BaseModel, model_validator
from lxml import etree, html as lxmlhtml
from transformers import AutoTokenizer
from langchain_text_splitters import RecursiveCharacterTextSplitter

from .tokenizer import BGE_M3_TOKENIZER


class TitleType(str, Enum):
    h1 = "h1"
    h2 = "h2"
    h3 = "h3"
    h4 = "h4"
    h5 = "h5"
    h6 = "h6"
    strong = "strong"
    span_strong = "span_strong"


class ChunkType(str, Enum):
    paragraph = "paragraph"
    toc = "toc"
    doc_title = "doc_title"


class ChunkNode(BaseModel):
    tag: str
    plain_content: str
    html_content: str
    token_counts: int
    title_type: TitleType = None

    origin_plain_content: Optional[str] = None
    xpath: str | None = None
    bboxes: List[dict] | None = None


    @model_validator(mode="after")
    def tag_lower(self):
        self.tag = self.tag.lower()
        return self

    @model_validator(mode="after")
    def analyze_title_type(self):
        if self.tag.startswith("h") and self.tag in TitleType.__members__.values():
            self.title_type = TitleType(self.tag)
        elif len(self.plain_content) <= 24 and self.tag == "strong":
            self.title_type = TitleType.strong
        elif len(self.plain_content) <= 24 and self.tag == "span" and "strong" in self.html_content:
            self.title_type = TitleType.span_strong
        else:
            pass
        return self

    @model_validator(mode="after")
    def complete_origin_plain_content(self):
        if self.origin_plain_content is None:
            self.origin_plain_content = self.plain_content
        return self


class Chunk(BaseModel):
    nodes: List[ChunkNode] = []
    title: List[str] | None = []

    token_counts: int = 0
    plain_content: str = ""
    origin_plain_content: str = ""
    vector: List[int] = None
    start_offset: int = None
    end_offset: int = None
    bboxes: List[dict] = []
    type_: str | ChunkType = ChunkType.paragraph


    def add_node(self, node: ChunkNode):
        self.token_counts += node.token_counts

        self.nodes.append(node)

        if node.bboxes:
            self.bboxes.extend(node.bboxes)

        self.origin_plain_content += node.origin_plain_content

        if (
                self.type_ == ChunkType.paragraph
                and (len(self.title) < 3
                and node.title_type is not None
                and ((not self.nodes) or (self.nodes[-1]).title_type is not None))
                and node.plain_content not in self.title):
            self.title.append(node.plain_content)
        else:
            self.plain_content += node.plain_content.strip()

        # 对于不同的标签,或新标签内容为空白时,视为换行.追加换行符
        # 否则不追加换行符
        if self.nodes:
            if self.nodes[-1].tag != node.tag or node.origin_plain_content.strip() == "":
                self.origin_plain_content += "\n"
                self.plain_content += "\n"


    @model_validator(mode="after")
    def init_fix(self):
        # 避免前置方法默认值传值造成的问题
        if self.title is None:
            self.title = []

        return self


class Chunker:
    replace_one_white_pattern = re.compile(r"(?<![a-zA-Z\W0-9])\s(?![a-zA-Z\W0-9])")
    remove_html_blank = re.compile(r'(?<=>)\s+(?=<)', )

    def __init__(self,
                 tokenizer: AutoTokenizer = BGE_M3_TOKENIZER,
                 max_tokens: int = 1024,
                 max_tolerance_tokens: int = 2048):

        self.max_tokens = max_tokens
        self.max_tolerance_tokens = max_tolerance_tokens
        self.tokenizer = tokenizer

        self.chunks: list[Chunk] = []
        self._last_node: ChunkNode | None = None

    def chunk(self, **kwargs) -> List[Chunk]:...

    def add_splittable_symbol(self):
        """如果前个chunk的尾节点,不等于后个chunk的首节点,则在前个chunk的尾节点追加换行符.主要为全文档纯文本构造做准备"""
        for i, chunk in enumerate(self.chunks):
            if i == 0:
                continue
            if self.chunks[i-1].nodes[-1].tag != chunk.nodes[0].tag and self.chunks[i-1].plain_content[-1] != "\n":
                self.chunks[i-1].origin_plain_content += "\n"

    def assign_chunk_offsets(self):
        """
        根据plain_content计算并分配每一个chunk的start_offset和end_offset
        简单地按照chunk的顺序累计计算偏移量
        """
        if not self.chunks:
            return

        current_offset = 0

        for i, chunk in enumerate(self.chunks):
            chunk_content = chunk.origin_plain_content
            chunk_length = len(chunk_content)

            # 设置当前chunk的偏移量
            chunk.start_offset = current_offset
            chunk.end_offset = current_offset + chunk_length

            # 更新下一个chunk的起始位置
            current_offset = chunk.end_offset

    def add_node(self, tag: str, html_content: str, plain_content: str, token_counts: int,
                 xpath: str = None, bboxes: List[dict] = None, origin_plain_content: str = None,
                 title: List[str] = None, type_: ChunkType | str = ChunkType.paragraph):
        """
        将节点添加到当前块或如果节点可以分割则创建新块。该方法对纯文本内容进行编码以计算token数量，
        判断块是否可分割，并适当地将节点添加到最后一个块或新创建的块中。

        参数:
            tag (str): 要添加的节点的标签名称。
            html_content (str): 节点内容的HTML表示。
            plain_content (str): 节点内容的纯文本表示。
            token_counts (int): 节点内容的token数量。
            xpath (str): 文档中节点的XPath位置。
            bboxes (List[dict]): 节点内容的bbox信息,用于后续定位标记
            origin_plain_content (str): 原始文本,仅用来做记录和拼接正文.不存在时取plain_content
            type_: 所允许的chunk_type类型,以做切分和筛选
        """
        node = ChunkNode(
            tag=tag, plain_content=plain_content, html_content=html_content, token_counts=token_counts, xpath=xpath,
            bboxes=bboxes, origin_plain_content=origin_plain_content
        )

        if (self._is_chunk_splittable(node=node)
                or self._last_node is None
                or type_ != self.chunks[-1].type_):
            self.chunks.append(Chunk(title=title, type_=type_))

        self._last_chunk.add_node(node)

        self._last_node = node

    def chunk_large_table_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        按行切换大型表格，支持合并单元格的识别和重构
        Args:
            element: 表格元素
            root: 根元素树

        Returns:

        """
        # 提取表头信息
        header_elements = element.xpath('.//tr[th]')
        header_html = ""
        header_plain_text = ""

        if header_elements:
            # 获取第一个包含th的tr作为表头
            header_row = header_elements[0]
            header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
            header_plain_text = self._extract_plain_text(element=header_elements)

        # 处理每一行数据
        data_rows = element.xpath(".//tr[td]")  # 获取包含td的行

        # 构建表格结构矩阵来处理合并单元格
        table_matrix = self._build_table_matrix(data_rows)

        # 按逻辑行分组处理
        logical_row_groups = self._group_logical_rows(table_matrix, data_rows)

        for group_index, (row_indices, logical_structure) in enumerate(logical_row_groups):
            # 重构当前逻辑行组的HTML
            reconstructed_html = self._reconstruct_table_html(
                data_rows, row_indices, logical_structure, header_html
            )
            reconstructed_html = self.remove_html_blank.sub("", reconstructed_html)

            # 构建完整的表格HTML
            if header_html:
                combined_html = f"<table><tbody>{header_html}{reconstructed_html}</tbody></table>"
            else:
                combined_html = f"<table><tbody>{reconstructed_html}</tbody></table>"

            # 直接从重构后的HTML中提取纯文本，避免重复
            group_plain_text = self._extract_plain_text_from_html(combined_html)

            # 构建纯文本内容
            if header_html and group_index == 0:
                # 第一组包含表头信息
                full_plain_content = group_plain_text
                origin_content = group_plain_text + "\n"
            else:
                # 其他组只包含当前组的内容
                if header_html:
                    # 如果有表头，需要组合表头和当前组内容
                    header_only_html = f"<table><tbody>{header_html}</tbody></table>"
                    header_text = self._extract_plain_text_from_html(header_only_html)
                    group_only_html = f"<table><tbody>{reconstructed_html}</tbody></table>"
                    group_only_text = self._extract_plain_text_from_html(group_only_html)
                    full_plain_content = header_text + " " + group_only_text
                    origin_content = group_only_text + "\n"
                else:
                    full_plain_content = group_plain_text
                    origin_content = group_plain_text + "\n"

            # 计算token数量
            combined_token_counts = len(self.tokenizer.encode(text=combined_html))

            # 使用第一行的xpath作为代表
            first_row_xpath = root.getpath(data_rows[row_indices[0]])

            self.add_node(
                tag=f"table_part",
                xpath=first_row_xpath,
                html_content=combined_html,
                plain_content=full_plain_content,
                origin_plain_content=origin_content,
                token_counts=combined_token_counts
            )

    def _build_table_matrix(self, data_rows):
        """
        构建表格矩阵来处理合并单元格
        返回一个矩阵，记录每个位置的单元格信息
        """
        if not data_rows:
            return []

        # 预估最大列数
        max_cols = 0
        for row in data_rows:
            cells = row.xpath('.//td')
            col_count = sum(int(cell.get('colspan', 1)) for cell in cells)
            max_cols = max(max_cols, col_count)

        # 初始化矩阵
        matrix = []
        for row_idx, row in enumerate(data_rows):
            if row_idx >= len(matrix):
                matrix.append([None] * max_cols)

            cells = row.xpath('.//td')
            col_idx = 0

            for cell in cells:
                # 找到下一个空位置
                while col_idx < len(matrix[row_idx]) and matrix[row_idx][col_idx] is not None:
                    col_idx += 1

                if col_idx >= len(matrix[row_idx]):
                    break

                colspan = int(cell.get('colspan', 1))
                rowspan = int(cell.get('rowspan', 1))

                # 填充当前单元格占用的所有位置
                for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                    # 确保有足够的行
                    while r >= len(matrix):
                        matrix.append([None] * max_cols)

                    for c in range(col_idx, min(col_idx + colspan, max_cols)):
                        if c < len(matrix[r]):
                            matrix[r][c] = {
                                'cell': cell,
                                'row_idx': row_idx,
                                'col_idx': col_idx,
                                'colspan': colspan,
                                'rowspan': rowspan,
                                'is_origin': (r == row_idx and c == col_idx)
                            }

                col_idx += colspan

        return matrix

    def _group_logical_rows(self, table_matrix, data_rows):
        """
        根据合并单元格将行分组为逻辑行组
        """
        if not table_matrix or not data_rows:
            return []

        groups = []
        processed_rows = set()

        for row_idx in range(len(data_rows)):
            if row_idx in processed_rows:
                continue

            # 找到当前行涉及的所有行（由于rowspan）
            involved_rows = set([row_idx])

            if row_idx < len(table_matrix):
                for cell_info in table_matrix[row_idx]:
                    if cell_info and cell_info['is_origin']:
                        rowspan = cell_info['rowspan']
                        for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                            involved_rows.add(r)

            # 构建逻辑结构
            logical_structure = self._build_logical_structure(table_matrix, list(involved_rows))

            groups.append((sorted(involved_rows), logical_structure))
            processed_rows.update(involved_rows)

        return groups

    def _build_logical_structure(self, table_matrix, row_indices):
        """
        为一组行构建逻辑结构
        """
        if not row_indices or not table_matrix:
            return []

        structure = []
        max_cols = len(table_matrix[0]) if table_matrix else 0

        for col_idx in range(max_cols):
            column_cells = []
            for row_idx in row_indices:
                if row_idx < len(table_matrix) and col_idx < len(table_matrix[row_idx]):
                    cell_info = table_matrix[row_idx][col_idx]
                    if cell_info and cell_info['is_origin']:
                        column_cells.append(cell_info)
            structure.append(column_cells)

        return structure

    def _reconstruct_table_html(self, data_rows, row_indices, logical_structure, header_html=""):
        """
        重构表格HTML，保持合并单元格的结构，避免重复单元格
        """
        if not row_indices:
            return ""

        # 收集所有需要的单元格及其起始行
        cells_with_origin_row = {}
        for column_cells in logical_structure:
            for cell_info in column_cells:
                cell_id = id(cell_info['cell'])
                origin_row = cell_info['row_idx']
                cells_with_origin_row[cell_id] = origin_row

        # 重构HTML，确保每个合并单元格只在其起始行出现
        reconstructed_rows = []
        for row_idx in row_indices:
            if row_idx >= len(data_rows):
                continue

            row = data_rows[row_idx]
            cells = row.xpath('.//td')

            # 只包含属于当前逻辑组且在当前行起始的单元格
            filtered_cells = []
            for cell in cells:
                cell_id = id(cell)
                if cell_id in cells_with_origin_row and cells_with_origin_row[cell_id] == row_idx:
                    filtered_cells.append(cell)

            if filtered_cells:
                # 重构行HTML
                row_html = "<tr>"
                for cell in filtered_cells:
                    cell_html = lxmlhtml.tostring(cell, encoding="unicode", pretty_print=False)
                    row_html += cell_html
                row_html += "</tr>"
                reconstructed_rows.append(row_html)

        return "".join(reconstructed_rows)

    def _extract_group_plain_text(self, data_rows, row_indices):
        """
        提取逻辑行组的纯文本内容
        """
        if not row_indices:
            return ""

        text_fragments = []
        for row_idx in row_indices:
            if row_idx < len(data_rows):
                row_text = self._extract_plain_text(data_rows[row_idx])
                if row_text.strip():
                    text_fragments.append(row_text)

        return " ".join(text_fragments)

    def _extract_plain_text_from_html(self, html_content):
        """
        从HTML表格中提取纯文本，在不同单元格之间添加空格分隔
        避免合并单元格导致的重复内容，在词汇级别进行去重
        """
        if not html_content:
            return ""

        try:
            # 解析HTML
            tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())

            # 收集所有单元格文本，然后在词汇级别去重
            all_cell_texts = []

            # 按行处理，收集所有单元格文本
            for row in tree.xpath('.//tr'):
                for cell in row.xpath('.//td | .//th'):
                    # 提取单元格内的所有文本，去除多余空白
                    cell_text_fragments = []
                    for text in cell.xpath('.//text()[normalize-space() != ""]'):
                        text = self.replace_one_white_pattern.sub("", text.strip())
                        if text:
                            cell_text_fragments.append(text)

                    cell_text = " ".join(cell_text_fragments).replace("\n", "")
                    if cell_text.strip():
                        all_cell_texts.append(cell_text.strip())

            # 将所有单元格文本连接，然后在词汇级别去重
            combined_text = " ".join(all_cell_texts)

            # 分词并去重，保持原始顺序
            words = combined_text.split()
            unique_words = []
            seen_words = set()

            for word in words:
                if word not in seen_words:
                    unique_words.append(word)
                    seen_words.add(word)

            return " ".join(unique_words)

        except Exception as e:
            # 如果HTML解析失败，回退到原始方法
            return self._extract_plain_text_fallback(html_content)

    def _extract_plain_text_fallback(self, html_content):
        """
        HTML解析失败时的回退方法
        """
        try:
            # 简单的文本提取，移除HTML标签
            import re
            # 移除HTML标签
            text = re.sub(r'<[^>]+>', ' ', html_content)
            # 规范化空白字符
            text = re.sub(r'\s+', ' ', text)
            return text.strip()
        except:
            return ""

    def chunk_large_text_tag(self, element: etree.Element, root: etree.ElementTree):
        """
        对于超长文本标签：先提取出标签下的纯文本，进行纯文本切分，然后将切分后的纯文本重新包装为html标签

        Args:
            element:
            root:

        Returns:

        """
        # 提取元素下的纯文本
        plain_text = self._extract_plain_text(element)

        # 使用文本分割器进行切分
        text_splitter = RecursiveCharacterTextSplitter(
            separators=["\n\n", "\n", "。", "！", "!", "？", "?", "，", " "],
            chunk_size=self.max_tokens,
            chunk_overlap=0
        )

        # 分割文本
        text_chunks = text_splitter.split_text(plain_text)
        # 获取原始标签名和xpath
        tag = element.tag.lower()
        xpath = root.getpath(element)

        # 为每个文本块创建HTML标签并添加节点
        for chunk_text in text_chunks:
            # 重新包装为HTML标签
            html_content = f"<{tag}>{chunk_text}</{tag}>"

            # 计算token数量
            token_counts = len(self.tokenizer.encode(text=html_content))

            # 添加节点
            self.add_node(
                tag=f"{tag}_part",
                xpath=xpath,
                html_content=html_content,
                plain_content=chunk_text,
                token_counts=token_counts
            )

    def _is_chunk_splittable(self, node: ChunkNode) -> bool:
        if not self.chunks:
            return False

        # token长度超过设定值,且上个节点包含正文
        if (self._last_chunk.token_counts + node.token_counts > self.max_tokens
                and self._last_chunk.plain_content.strip()):
            return True

        # 上一个节点非标题,但此节点为标题,进行切分
        if self._last_node and self._last_node.title_type is None and node.title_type:
            return True

        # 此节点为大节点拆分的行
        if node.tag.endswith("part"):
            return True

        return False

    def _extract_plain_text(self, element: etree.Element):
        element_fragments = []
        elements = [element] if not isinstance(element, list) else element
        for e in elements:
            for text in e.xpath('.//text()[normalize-space() != ""]'):
                text = self.replace_one_white_pattern.sub("", text.strip())
                element_fragments.append(text)

        return " ".join(element_fragments).replace("\n", "")

    @property
    def _last_chunk(self) -> Chunk | None:
        if not self.chunks:
            return None
        return self.chunks[-1]
