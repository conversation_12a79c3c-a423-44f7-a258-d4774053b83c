#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
from typing import List

from lxml import html as lxmlhtml
from transformers import AutoTokenizer

from common.logger import logger
from controller.parser.chunker.base import Chunker, Chunk, ChunkNode, ChunkType


class MineUPDFChunker(Chunker):
    def __init__(self,
                 tokenizer: AutoTokenizer,
                 max_tokens: int = 512,  # 相对于基础切分的1024,size更小.
                 max_tolerance_tokens: int = 2048):  # 此参数在此模块未生效
        super().__init__(max_tokens=max_tokens,
                         max_tolerance_tokens=max_tolerance_tokens,
                         tokenizer=tokenizer)

        self._last_node: ChunkNode | None = None


    def chunk(self, parsed_blocks: List[dict]) -> List[Chunk]:
        title = []
        for i, block in enumerate(parsed_blocks):
            html_tree = lxmlhtml.fromstring(block["html"])
            tag = html_tree.tag
            # 由于mineru解析tag标签完全可控,直接以枚举进行处理

            if tag.startswith("h"):
                title = []

            img_elements = html_tree.xpath('.//img')
            if img_elements:
                # 删除 img 元素
                for img in img_elements:
                    img.getparent().remove(img)
                html_content = lxmlhtml.tostring(html_tree, encoding="unicode", pretty_print=False)
                token_counts = len(self.tokenizer.encode(text=html_content))
            else:
                token_counts = len(self.tokenizer.encode(text=block["html"]))

            chunk_type = ChunkType.paragraph
            if tag == "ol":
                chunk_type = ChunkType.toc
            if tag == "h1":
                chunk_type = ChunkType.doc_title

            self.add_node(
                tag=tag,
                html_content=block["html"],
                plain_content=block["plain_text"],
                token_counts=token_counts,
                bboxes=block["bboxes"],
                title=title,
                type_=chunk_type
            )

            # 从下个节点开始title生效
            if tag.startswith("h"):
                title = [block["plain_text"]]

        return self.chunks


if __name__ == '__main__':
    import json
    from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER

    with open(r"D:\project\llm_da\test.json", mode="r", encoding="utf-8") as f:
        doc_blocks = json.load(f)

    MineUPDFChunker(tokenizer=BGE_M3_TOKENIZER).chunk(doc_blocks)