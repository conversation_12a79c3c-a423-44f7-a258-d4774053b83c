#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pathlib import Path

from transformers import AutoTokenizer


def make_tokenizer(model_path: Path, model_name):
    if model_path and model_path.exists():
        return AutoTokenizer.from_pretrained(model_path)
    else:
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(model_path)
        return tokenizer


# 获取项目根目录
def get_src_dir():
    """获取src目录的绝对路径，无论当前工作目录在哪里"""
    current_path = Path(__file__).resolve()

    # 向上查找直到找到src目录
    while current_path.name != 'src':
        current_path = current_path.parent
        # 防止无限循环（到达文件系统根目录）
        if current_path.parent == current_path:
            raise RuntimeError("无法找到src目录")

    return current_path.parent

# 获取src目录的绝对路径
SRC_DIR = get_src_dir()
# 确保资源目录存在
RESOURCES_DIR = SRC_DIR / "resources"
RESOURCES_DIR.mkdir(exist_ok=True)

# 使用绝对路径
BGE_M3_TOKENIZER = make_tokenizer(
    model_name="BAAI/bge-m3", 
    model_path=RESOURCES_DIR / "bge_m3_tokenizer"
)
