from engine.cache import r_cache


class CacheMemory:

    @staticmethod
    async def add_short_memory(research_id: str, plan_id: str, data: dict):
        """
        Add research data to the cache.
        """
        await r_cache.hset(name=research_id, key=plan_id, value=data)
        await r_cache.expire(name=research_id, time=3600)

    @staticmethod
    async def get_short_memory(research_id: str, plan_id: str):
        """
        Get research data from the cache.
        """
        return await r_cache.hget(name=research_id, key=plan_id)

    @staticmethod
    async def add_long_memory(research_id: str, data: dict):
        """
        Add long-term research data to the cache.
        """
        await r_cache.hset(name=research_id, key="long_term", value=data)
        await r_cache.expire(name=research_id, time=3600)

    @staticmethod
    async def get_long_memory(research_id: str):
        """
        Get long-term research data from the cache.
        """
        return await r_cache.hget(name=research_id, key="long_term")
