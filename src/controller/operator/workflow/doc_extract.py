import asyncio
from typing import List

from pydantic import BaseModel

from config import LLMModel
from controller.repository import DocAnalysis
from controller.operator.chunking import DocModel, BaseChunking, SlidingWindowChunking, ExtractModel
from controller.operator.runner.base import BaseRunner
from controller.operator.runner.doc_extract import BaseExtract, SentimentAnalysis, CustomExtract, CombineAbstract
from controller.operator.workflow import BaseWorkflow


class DocExtractWorkflowModel(BaseModel):
    BaseExtractModel: LLMModel = LLMModel.DEEPSEEK_CHAT
    SentimentAnalysisModel: LLMModel = LLMModel.DEEPSEEK_CHAT
    CustomExtractModel: LLMModel = LLMModel.DEEPSEEK_CHAT
    CombineAbstractModel: LLMModel = LLMModel.DEEPSEEK_CHAT

class DocExtractConfigModel(BaseModel):
    UseBaseExtract: bool = True
    UseSentimentAnalysis: bool = True
    UseCustomExtract: bool = True


class DocExtractWorkflow(BaseWorkflow):
    def __init__(self, doc: DocModel, chunking: BaseChunking = SlidingWindowChunking(), user_prompt: str = None,
                 update_index: str = None, update_id: str = None, model: DocExtractWorkflowModel = DocExtractWorkflowModel(),
                 config: DocExtractConfigModel = DocExtractConfigModel()):
        super().__init__()

        self.doc = doc
        self.chunking = chunking
        self.user_prompt = user_prompt

        self.update_index = update_index
        self.update_id = update_id
        self.model = model
        self.config = config
        self.thinking_content = []

        assert doc.content, ValueError("文档内容为空")

    async def _run(self) -> dict:
        self.doc.chunk_children = self.chunking.chunk(content=self.doc.content).chunk_children

        if len(self.doc.chunk_children) == 1:
            content_text = f"文章标题：{self.doc.title}\n文章内容：{self.doc.content}"

            runners: List[BaseRunner] = [
                BaseExtract(content=content_text, token_consumption=self.token_consumption,
                            model_name=self.model.BaseExtractModel) if self.config.UseBaseExtract else BaseRunner(),
                SentimentAnalysis(content=content_text, token_consumption=self.token_consumption,
                                  model_name=self.model.SentimentAnalysisModel) if self.config.UseSentimentAnalysis else BaseRunner(),
                CustomExtract(content=content_text, user_prompt=self.user_prompt, token_consumption=self.token_consumption,
                              model_name=self.model.CustomExtractModel) if self.user_prompt and self.config.UseCustomExtract else BaseRunner()]

            base_result, sentiment_result, custom_result = await self.run_with_concurrency(runners=runners)

            thinking_message = {
                "chunk_content": self.doc.content,
                "thinking_content_list": [],
            }

            if self.config.UseBaseExtract:
                thinking_message["thinking_content_list"].append({
                    "from": "BaseExtract",
                    "thinking_content": base_result.extra
                })
            if self.config.UseSentimentAnalysis:
                thinking_message["thinking_content_list"].append({
                    "from": "SentimentAnalysis",
                    "thinking_content": sentiment_result.extra
                })
            if self.config.UseCustomExtract and self.user_prompt:
                thinking_message["thinking_content_list"].append({
                    "from": "CustomExtract",
                    "thinking_content": custom_result.extra
                })
            self.thinking_content.append(thinking_message)

            extract_result = ExtractModel(
                subject=base_result.subject if self.config.UseBaseExtract else [],
                industry=base_result.industry if self.config.UseBaseExtract else [],
                positive_view=base_result.positive_view if self.config.UseBaseExtract else [],
                negative_view=base_result.negative_view if self.config.UseBaseExtract else [],
                sentiment_score=sentiment_result.SF if self.config.UseSentimentAnalysis else 0.0,
                keywords=base_result.keywords if self.config.UseBaseExtract else [],
                abstract=base_result.abstract if self.config.UseBaseExtract else "",
            )

            if self.user_prompt:
                extract_result.analysis = custom_result

        else:
            base_extract_runners = [
                BaseExtract(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    token_consumption=self.token_consumption,
                    model_name=self.model.BaseExtractModel
                )
                for chunk in self.doc.chunk_children
            ] if self.config.UseBaseExtract else []
            sentiment_analysis_runners = [
                SentimentAnalysis(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    token_consumption=self.token_consumption,
                    model_name=self.model.SentimentAnalysisModel
                )
                for chunk in self.doc.chunk_children
            ] if self.config.UseSentimentAnalysis else []
            custom_extract_runners = [
                CustomExtract(
                    content=f"文章标题：{self.doc.title}\n文章片段：{chunk.chunk_text}",
                    user_prompt=self.user_prompt,
                    token_consumption=self.token_consumption,
                    model_name=self.model.CustomExtractModel
                )
                for chunk in self.doc.chunk_children
            ] if self.user_prompt and self.config.UseCustomExtract else []

            coroutines = [
                self.run_with_concurrency(base_extract_runners),
                self.run_with_concurrency(sentiment_analysis_runners),
                self.run_with_concurrency(custom_extract_runners)
            ]

            base_extract_results, sentiment_analysis_results, custom_extract_results = await asyncio.gather(*coroutines)

            extract_result = ExtractModel(
                subject=list({item for chunk in base_extract_results for item in chunk.subject}) if self.config.UseBaseExtract else [],
                industry=list({item for chunk in base_extract_results for item in chunk.industry}) if self.config.UseBaseExtract else [],
                positive_view=[item for chunk in base_extract_results for item in chunk.positive_view] if self.config.UseBaseExtract else [],
                negative_view=[item for chunk in base_extract_results for item in chunk.negative_view] if self.config.UseBaseExtract else [],
                sentiment_score=sum(chunk.SF for chunk in sentiment_analysis_results) / len(sentiment_analysis_results) if self.config.UseSentimentAnalysis else 0.0,
                keywords=list({item for chunk in base_extract_results for item in chunk.keywords}) if self.config.UseBaseExtract else [],
            )

            for index, chunk_children in enumerate(self.doc.chunk_children):
                thinking_message = {
                    "chunk_content": chunk_children.chunk_text,
                    "thinking_content_list": [],
                }

                if self.config.UseBaseExtract:
                    thinking_message["thinking_content_list"].append({
                        "from": "BaseExtract",
                        "thinking_content": base_extract_results[index].extra
                    })
                if self.config.UseSentimentAnalysis:
                    thinking_message["thinking_content_list"].append({
                        "from": "SentimentAnalysis",
                        "thinking_content": sentiment_analysis_results[index].extra
                    })
                if self.user_prompt and self.config.UseCustomExtract:
                    thinking_message["thinking_content_list"].append({
                        "from": "CustomExtract",
                        "thinking_content": custom_extract_results[index].extra
                    })

                self.thinking_content.append(thinking_message)

            runners = [
                CombineAbstract(
                    content_list=[chunk.abstract for chunk in base_extract_results],
                    token_consumption=self.token_consumption,
                    model_name=self.model.CombineAbstractModel
                ) if self.config.UseBaseExtract else BaseRunner(),
                CombineAbstract(
                    content_list=[custom_extract for custom_extract in custom_extract_results],
                    token_consumption=self.token_consumption,
                    model_name=self.model.CombineAbstractModel
                ) if self.user_prompt and self.config.UseCustomExtract else BaseRunner()
            ]
            combine_abstract, custom_abstract = await self.run_with_concurrency(runners)

            if self.config.UseBaseExtract:
                extract_result.abstract = combine_abstract
            if self.user_prompt and self.config.UseCustomExtract:
                extract_result.analysis = custom_abstract

        if self.update_index and self.update_id:
            await DocAnalysis.update(
                index=self.update_index,
                _id=self.update_id,
                doc={"extract_result": extract_result.model_dump()}
            )

        return extract_result.model_dump()


if __name__ == '__main__':
    import json

    from controller.operator.example_text import long_text

    doc_item = DocModel(content=long_text)
    workflow = DocExtractWorkflow(doc=doc_item, config=DocExtractConfigModel(UseBaseExtract=True))
    res = asyncio.run(workflow.run())
    print(json.dumps(res, indent=4, ensure_ascii=False))
    print(json.dumps(workflow.thinking_content, indent=4, ensure_ascii=False))

    thinking_content = [
        {
            "chunk_content": "文本分段1",
            "thinking_content_list": [
                {
                    "from": "BaseExtract",
                    "thinking_content": "这是一个长文本示例，用于测试文档提取工作流的功能。"
                },
                {
                    "from": "SentimentAnalysis",
                    "thinking_content": "情感分析结果：积极"
                },
                {
                    "from": "CustomExtract",
                    "thinking_content": "自定义提取结果：这是一个自定义提取的结果。"
                }
            ]
        },
        {
            "chunk_content": "文本分段2",
            "thinking_content_list": [
                {
                    "from": "BaseExtract",
                    "thinking_content": "这是一个长文本示例，用于测试文档提取工作流的功能。"
                },
                {
                    "from": "SentimentAnalysis",
                    "thinking_content": "情感分析结果：积极"
                },
                {
                    "from": "CustomExtract",
                    "thinking_content": "自定义提取结果：这是一个自定义提取的结果。"
                }
            ]
        },
        {...}
    ]
