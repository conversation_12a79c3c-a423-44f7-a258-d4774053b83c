import re
import json
from enum import StrEnum
from typing import List, Annotated, Optional, Union, Literal

from pydantic import BaseModel, Field, model_validator

from config import LLMModel
from controller.operator.tool.get_citations import get_abstract_citations
from controller.operator.runner.analyze import AbstractQuantify, AbstractReport, FinalReport, GetCitation
from controller.operator.workflow import BaseWorkflow


class AbstractResultModel(BaseModel):
    index: Annotated[str, Field(title="索引")]
    abstract_content: Annotated[str, Field(title="摘要内容")]


class IndicatorType(StrEnum):
    NUMBER = "number"
    ENUM = "enum"
    STRING = "string"


class NumberConfig(BaseModel):
    min_value: Annotated[Optional[float], Field(title="最小值")] = None
    max_value: Annotated[Optional[float], Field(title="最大值")] = None
    step: Annotated[Optional[float], Field(title="步长")] = None


class EnumOption(BaseModel):
    """枚举选项，支持不同的数据类型"""
    label: Annotated[str, Field(title="选项标签")]
    value: Annotated[Union[int, float, str], Field(title="选项值")]
    value_type: Annotated[Literal["int", "float", "str"], Field(title="值类型")]

    @model_validator(mode='after')
    def validate_value_type(self) -> 'EnumOption':
        """验证值类型与实际值类型匹配"""
        if self.value_type == "int" and not isinstance(self.value, int):
            raise ValueError(f"值类型指定为int，但实际值 {self.value} 不是整数类型")
        elif self.value_type == "float" and not isinstance(self.value, float):
            raise ValueError(f"值类型指定为float，但实际值 {self.value} 不是浮点数类型")
        elif self.value_type == "str" and not isinstance(self.value, str):
            raise ValueError(f"值类型指定为str，但实际值 {self.value} 不是字符串类型")
        return self


class StringConfig(BaseModel):
    min_length: Annotated[Optional[int], Field(title="最小长度")] = None
    max_length: Annotated[Optional[int], Field(title="最大长度")] = None
    pattern: Annotated[Optional[str], Field(title="正则表达式模式")] = None


class EnumConfig(BaseModel):
    options: Annotated[List[EnumOption], Field(title="枚举选项")]


class QuantifyIndicatorModel(BaseModel):
    indicator_name: Annotated[str, Field(title="指标名称")]  # 禁止传入重复的指标名称，后续不再进行校验
    indicator_description: Annotated[str, Field(title="指标描述")]
    indicator_type: Annotated[IndicatorType, Field(title="指标类型")]

    # 针对不同类型的配置
    number_config: Annotated[Optional[NumberConfig], Field(title="数字类型配置")] = None
    enum_config: Annotated[Optional[EnumConfig], Field(title="枚举类型配置")] = None
    string_config: Annotated[Optional[StringConfig], Field(title="字符串类型配置")] = None

    @model_validator(mode='after')
    def validate_config_match_type(self) -> 'QuantifyIndicatorModel':
        """验证配置与类型匹配"""
        if self.indicator_type == IndicatorType.NUMBER and not self.number_config:
            raise ValueError("数字类型指标必须提供 number_config")
        if self.indicator_type == IndicatorType.ENUM and not self.enum_config:
            raise ValueError("枚举类型指标必须提供 enum_config")
        if self.indicator_type == IndicatorType.STRING and not self.string_config:
            raise ValueError("字符串类型指标必须提供 string_config")
        return self


class AnalyzeWorkflowModel(BaseModel):
    AbstractQuantifyModel: LLMModel = LLMModel.DEEPSEEK_CHAT
    AbstractReportModel: LLMModel = LLMModel.DEEPSEEK_CHAT
    FinalReportModel: LLMModel = LLMModel.GEMINI_2_5_FLASH
    GetCitation: LLMModel = LLMModel.DEEPSEEK_CHAT


class AnalyzeWorkflow(BaseWorkflow):
    def __init__(self, abstract_result_list: List[AbstractResultModel], user_prompt: str, extra_prompt: str = None,
                 indicator_list: List[QuantifyIndicatorModel] = None, analyze_name: str = None, tags: list[str] = None,
                 extra: dict = None, model: AnalyzeWorkflowModel = AnalyzeWorkflowModel()):
        super().__init__()

        self.abstract_result_list = abstract_result_list
        self.user_prompt = user_prompt
        self.extra_prompt = extra_prompt
        self.indicator_list = indicator_list
        self.analyze_task_info = f"分析任务名称：{analyze_name}，分析任务标签：{tags}，分析任务具体描述：{user_prompt}"
        self.quantify_task_info = [
            f"指标名称：{indicator.indicator_name}，指标描述：{indicator.indicator_description}，指标类型：{indicator.indicator_type}"
            for indicator in indicator_list
        ] if indicator_list else []

        # 记录业务信息,方便批量执行后写入任务结果
        self.extra = extra
        self.model = model

    @staticmethod
    def remove_citations(text):
        """
        Remove all occurrences of [citation:xxx] from the input text.
        """
        # Pattern matches [citation:后面是1个或多个数字或字母]
        pattern = r'\[citation:[^\]]+\]'
        return re.sub(pattern, '', text)

    async def _run(self):
        runners = [GetCitation(
            content=abstract_result.abstract_content,
            analyze_task_info=self.analyze_task_info,
            token_consumption=self.token_consumption,
            model_name=self.model.GetCitation,
        ) for abstract_result in self.abstract_result_list]

        get_citation_result = await self.run_with_concurrency(runners)

        abstract_list = []
        for i, abstract_result in enumerate(self.abstract_result_list):
            citation_list = get_abstract_citations(abstract_result.index, get_citation_result[i].citation_id_list)
            citation_list = [{
                "title": citation.title,
                "content": citation.extract_result.abstract if citation.extract_result else citation.content,
                "data_time": citation.data_time
            } for citation in citation_list if citation is not None]
            abstract_list.append({
                "abstract_content": self.remove_citations(abstract_result.abstract_content),
                "citation_list": citation_list,
            })

        if len(f"{json.dumps(abstract_list, ensure_ascii=False, indent=4)}") > 1024 * 32:
            runners = [AbstractReport(
                content=abstract,
                analyze_task_info=self.analyze_task_info,
                quantify_task_info=self.quantify_task_info,
                token_consumption=self.token_consumption,
                model_name=self.model.AbstractReportModel,
            ) for abstract in abstract_list]

            abstract_list = await self.run_with_concurrency(runners)

        quantify_task_info = []
        for quantify_indicator in self.indicator_list:
            if quantify_indicator.indicator_type == IndicatorType.NUMBER:
                config = quantify_indicator.number_config.model_dump()
            elif quantify_indicator.indicator_type == IndicatorType.STRING:
                config = quantify_indicator.string_config.model_dump()
            elif quantify_indicator.indicator_type == IndicatorType.ENUM:
                config = quantify_indicator.enum_config.model_dump()
            else:
                config = None

            quantify_task_info.append({
                "indicator_name": quantify_indicator.indicator_name,
                "indicator_description": quantify_indicator.indicator_description,
                "indicator_type": quantify_indicator.indicator_type,
                "config": config,
            })

        runners = [
            AbstractQuantify(
                abstract_list=abstract_list,
                extra_prompt=self.extra_prompt,
                analyze_task_info=self.analyze_task_info,
                quantify_task_info=self.quantify_task_info,
                token_consumption=self.token_consumption,
                model_name=self.model.AbstractQuantifyModel,
            ),
            FinalReport(
                abstract_list=abstract_list,
                extra_prompt=self.extra_prompt,
                analyze_task_info=self.analyze_task_info,
                token_consumption=self.token_consumption,
                model_name=self.model.FinalReportModel
            )]
        abstract_quantify_result, final_report_result = await self.run_with_concurrency(runners)
        return abstract_quantify_result.indicators, final_report_result
