import asyncio
import re
import time
import traceback
from abc import ABC, abstractmethod
from typing import Any, AsyncGenerator, List, Optional

from common.logger import logger
from common.snowflake import Snowflake
from controller.operator.runner.base import BaseRunner, RunnerStatus, TokenConsumption


WorkflowStatus = RunnerStatus


class BaseWorkflow(ABC):
    def __init__(self, concurrency: int = 20) -> None:
        """初始化工作流基类
        
        Args:
            concurrency: 并发数量限制，默认为20
        """
        self.id: int = Snowflake.get_id()
        self.status: WorkflowStatus = WorkflowStatus.READY
        
        self.error_msg: Optional[str] = None
        self.response: Optional[Any] = None
        
        self.concurrency: int = concurrency
        self.semaphore: asyncio.Semaphore = asyncio.Semaphore(concurrency)
        self.token_consumption: TokenConsumption = TokenConsumption()

    async def run(self) -> Optional[Any]:
        """运行工作流
        
        Returns:
            工作流执行结果，失败时返回None
        """
        task = asyncio.current_task()
        task_name = task.get_name() if task else "unknown"
        start_time = time.time()
        
        logger.info(
            f"工作流开始运行 - 类型: {self.__class__.__name__}, "
            f"任务: {task_name}, ID: {self.id}"
        )
        self.status = WorkflowStatus.RUNNING

        try:
            result = await self._run()
            self.status = WorkflowStatus.SUCCESS
            self.response = result
            
            execution_time = time.time() - start_time
            input_tokens = sum(self.token_consumption.input_token)
            output_tokens = sum(self.token_consumption.output_token)
            
            logger.info(
                f"工作流执行成功 - 类型: {self.__class__.__name__}, "
                f"任务: {task_name}, ID: {self.id}, "
                f"耗时: {execution_time:.2f}秒, "
                f"输入令牌总数: {input_tokens}, 输出令牌总数: {output_tokens}"
            )
            return result
            
        except Exception as e:
            self.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            self.status = WorkflowStatus.FAILED
            
            logger.error(
                f"工作流执行失败 - 类型: {self.__class__.__name__}, "
                f"任务: {task_name}, ID: {self.id}, "
                f"错误信息: {self.error_msg}"
            )
            return None

    async def run_stream(self) -> AsyncGenerator[Any, None]:
        """以流式方式运行工作流
        
        Yields:
            工作流的流式响应数据
        """
        async for response in self._run():
            yield response

    @abstractmethod
    async def _run(self, *args: Any, **kwargs: Any) -> Any:
        """具体的工作流执行逻辑，由子类实现
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            工作流执行结果
        """
        return "Success"

    async def run_with_concurrency(self, runners: List[BaseRunner]) -> List[Any]:
        """并发执行多个运行器
        
        Args:
            runners: 要执行的运行器列表
            
        Returns:
            所有运行器的执行结果列表
        """
        async def _run_with_concurrency(runner: BaseRunner) -> Any:
            """单个运行器的并发执行包装器"""
            async with self.semaphore:
                try:
                    result = await runner.run()
                    logger.debug(
                        f"运行器执行完成 - 工作流: {self.__class__.__name__}, "
                        f"运行器ID: {runner.id}"
                    )
                    
                    if runner.status != RunnerStatus.SUCCESS:
                        raise RuntimeError(f"运行器 {runner.id} 执行失败")
                    
                    return result
                except Exception as e:
                    logger.error(
                        f"运行器执行出错 - 工作流: {self.__class__.__name__}, "
                        f"运行器ID: {runner.id}, 错误: {str(e)}"
                    )
                    raise

        tasks = [_run_with_concurrency(runner) for runner in runners]
        return await asyncio.gather(*tasks, return_exceptions=True)

    @staticmethod
    def get_markdown_content(content: str) -> str:
        """从内容中提取markdown代码块
        
        Args:
            content: 包含markdown代码块的字符串
            
        Returns:
            提取的markdown内容，如果没有找到代码块则返回原内容
        """
        pattern = re.compile(r'```markdown(.*?)```', re.DOTALL)
        match = pattern.search(content)
        return match.group(1).strip() if match else content.strip()

    @staticmethod
    def get_json_content(content: str) -> str:
        """从内容中提取JSON代码块
        
        Args:
            content: 包含JSON代码块的字符串
            
        Returns:
            提取的JSON内容，如果没有找到代码块则返回原内容
        """
        pattern = re.compile(r'```json(.*?)```', re.DOTALL)
        match = pattern.search(content)
        return match.group(1).strip() if match else content.strip()
