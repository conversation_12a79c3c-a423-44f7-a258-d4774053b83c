from typing import List

from controller.operator.chunking import DocModel, ExtractModel
from engine.es import es_sync
from model.doc import ALL_REPO_INDEX
from model.summarizer_task_es import SUMMARIZER_TASK_INDEX


def get_abstract_citations(abstract_result_index: str, citation_ids: List[int]) -> List[DocModel]:
    """
    获取摘要引用的文献列表
    :param abstract_result_index: 摘要结果索引
    :param citation_ids: 引用的文献ID列表
    :return: 文献列表
    """
    source = es_sync.get_source(
        index=SUMMARIZER_TASK_INDEX,
        id=abstract_result_index,
        source_includes=["related_doc_ids"])

    related_doc_ids = source["related_doc_ids"]
    doc_ids = [related_doc_ids[i] for i in citation_ids]
    res = es_sync.search(
        index=ALL_REPO_INDEX,
        query={"terms": {"_id": doc_ids}},
        source_includes=["title", "content", "data_time"])
    hits = res["hits"]["hits"]

    doc_id_mapping = {doc["_id"]: doc["_source"] for doc in hits}
    docs = []
    for citation_id in citation_ids:
        if related_doc_ids[citation_id] in doc_id_mapping:
            doc = doc_id_mapping[related_doc_ids[citation_id]]
            docs.append(
                DocModel(
                    title=doc["title"],
                    content=doc["content"],
                    data_time=doc["data_time"][:10],
                    extract_result=ExtractModel(**doc["extract_result"]) if doc.get("extract_result") else None)
                )
        else:
            docs.append(None)

    return docs