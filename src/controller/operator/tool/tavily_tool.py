import asyncio
import json
import random
from typing import Optional, Annotated, List, Sequence

import httpx
from autogen_agentchat.messages import TextMessage, BaseChatMessage
from autogen_core import CancellationToken
from pydantic import BaseModel, Field

from config import TAVILY_API_URL, TAVILY_API_KEY
from controller.operator.agent.deep_research.searcher import SearcherSummaryAgent
from common.logger import logger


class TavilySearchResult(BaseModel):
    title: Annotated[str, Field(title="标题", description="搜索结果的标题")]
    url: Annotated[str, Field(title="URL", description="搜索结果的链接")]
    content: Annotated[str, Field(title="内容", description="搜索结果的内容摘要")]
    raw_content: Annotated[Optional[str], Field(title="原始内容", description="搜索结果的原始内容")]


class TavilySearchResponse(BaseModel):
    results: Annotated[List[TavilySearchResult], Field(title="搜索结果", description="Tavily搜索API返回的结果列表")]

# Tavily API密钥池
TAVILY_KEY_POOL = [
    # "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",
    # "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",
    # "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",
    # "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",
    # "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",
    "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",
    "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",
    "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",
    "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",
    "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",
]


async def tavily_search(query: str):
    """
    使用 Tavily 进行互联网搜索，获取最新的实时信息和相关内容。

    该工具可以帮助你获取最新的新闻、市场数据、公司信息、行业动态等网络信息。非常适合需要实时数据或最新信息的查询。

    Args:
        query: 搜索查询内容，应该是明确、具体的问题或关键词。例如"特斯拉最新季度财报"、"上海房价趋势"等，你应该尽量避免在 query 参数中使用3个以上的关键词，除非是非常必要的。

    """
    max_retries = 3
    retry_interval = 3
    attempt = 0
    last_exception = None

    while attempt < max_retries:
        try:
            attempt += 1

            api_key = TAVILY_API_KEY if TAVILY_API_KEY else random.choice(TAVILY_KEY_POOL)

            params = {
                "api_key": api_key,
                "query": query,
                "max_results": 10,
                # "search_depth": "basic",
                "include_domains": [],
                "exclude_domains": [],
                "include_answer": False,  # 是否包含大模型生成的答案
                "include_raw_content": True,
                "include_images": False,
                "include_image_descriptions": False,
            }

            async def process_single_result(res):
                """处理单个搜索结果"""
                content = res.raw_content if res.raw_content else res.content
                summary_agent = SearcherSummaryAgent()
                cancellation_token = CancellationToken()

                if len(content) > 5000:
                    content = content[:100000]
                    messages: Sequence[BaseChatMessage] = [
                        TextMessage(source="user", content=content),
                    ]
                    response = await asyncio.wait_for(
                        summary_agent.on_messages(messages, cancellation_token),
                        timeout=30
                    )
                    content = response.chat_message.content[:5000]

                return {
                    "title": res.title,
                    "url": res.url,
                    "content": content
                }

            async with httpx.AsyncClient(trust_env=True) as client:
                response = await client.post(url=TAVILY_API_URL, json=params, timeout=10)
                response.raise_for_status()
                search_result = TavilySearchResponse.model_validate(response.json())

                tasks = [
                    process_single_result(res)
                    for res in search_result.results
                ]

                # 并发执行所有任务
                ans = await asyncio.gather(*tasks, return_exceptions=True)

                # 过滤掉异常结果
                filtered_ans = []
                for result in ans:
                    if isinstance(result, Exception):
                        logger.warning(f"处理搜索结果时出错: {result}")
                    else:
                        filtered_ans.append(result)

                return json.dumps(filtered_ans, ensure_ascii=False)


        except httpx.ConnectError as e:
            last_exception = e
            logger.warning(f"连接 Tavily API 失败 (尝试 {attempt}/{max_retries}): {e}")
        except httpx.HTTPStatusError as e:
            last_exception = e
            logger.warning(f"Tavily API 返回错误状态码 (尝试 {attempt}/{max_retries}): {e.response.status_code} - {e.response.text}")
        except Exception as e:
            last_exception = e
            logger.warning(f"请求 Tavily API 异常 (尝试 {attempt}/{max_retries}): {e}")

        if attempt < max_retries:
            await asyncio.sleep(retry_interval)

    logger.error(f"请求 Tavily API 失败，已重试 {max_retries} 次: {last_exception}")
    raise last_exception


if __name__ == "__main__":
    result = asyncio.run(tavily_search(query="宁德时代成立时间 注册地 创始人背景 股权结构 历史演变 战略动因"))
    print(result)
