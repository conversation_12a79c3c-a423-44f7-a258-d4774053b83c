from autogen_core.models import <PERSON>t<PERSON>ompletionClient

from controller.engine import claude_4_sonnet
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.tool.time import get_current_time


class CoordinatorAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = claude_4_sonnet, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="coordinator",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message="使用工具获取当前时间",
            tools=[get_current_time]
        )


if __name__ == '__main__':
    import asyncio
    from typing import Sequence

    from autogen_agentchat.base import Response
    from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent, ToolCallRequestEvent
    from autogen_core import CancellationToken

    agent = CoordinatorAgent()
    messages: Sequence[BaseChatMessage] = [
        TextMessage(source="user", content="use get_time tool to get current time"),
    ]
    cancellation_token = CancellationToken()

    async def get_streaming_response():
        async for message in agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, TextMessage):
                print("text:", message.content)
            elif isinstance(message, ToolCallRequestEvent):
                print("tool call request:", message)
            elif isinstance(message, ToolCallExecutionEvent):
                print("tool call execution:", message)
            elif isinstance(message, Response):
                print("response:", message)
            else:
                print("unknown message type:", message)

    response = asyncio.run(get_streaming_response())
