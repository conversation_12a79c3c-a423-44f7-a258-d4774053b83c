from typing import Annotated

from autogen_core.models import ChatCompletionClient

from controller.engine import gemini_2_5_flash_lite, gpt_41
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.prompt.deep_research.searcher import searcher_summary_prompt
from controller.operator.tool.time import get_current_time


class SearcherSummaryAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = gemini_2_5_flash_lite, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="summary",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=searcher_summary_prompt.format(
                current_time=get_current_time()
            ),
        )
