from typing import Sequence

from autogen_agentchat.messages import BaseChatMessage, TextMessage
from autogen_core import CancellationToken
from autogen_core.models import ChatCompletionClient

from controller.engine import gemini_2_5_pro
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.prompt.deep_research.reporter import sub_reporter_prompt, reporter_prompt
from controller.operator.tool.time import get_current_time


class SubReporterAgent(BaseAgent):
    def __init__(self, sub_plan: str, research_report: str, model_client: ChatCompletionClient = gemini_2_5_pro, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="sub_reporter",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=sub_reporter_prompt.format(
                current_time=get_current_time(),
                sub_plan=sub_plan,
                research_report=research_report,
            ),
        )


class ReporterAgent(BaseAgent):
    def __init__(self, user: str, list_of_sub_reports: list, model_client: ChatCompletionClient = gemini_2_5_pro, token_consumption=None):

        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="reporter",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=reporter_prompt.format(
                current_time=get_current_time(),
                user=user,
                list_of_sub_reports=list_of_sub_reports,
            ),
        )


if __name__ == '__main__':
    import asyncio
    import json

    plans = [
        "检索宁德时代官方年报、招股书及公司官网，提取基础信息：成立时间、创始人背景、注册地、股权结构等基础档案。",
        "系统梳理公司发展关键阶段（2011-2024）：(a) 政策驱动期（2011-2015）：查阅工信部新能源补贴政策、公司早期客户绑定案例（如宝马合作）。(b) 技术突破期（2016-2020）：调研CTP技术专利数据、产能扩张地图（如宁德/德国工厂投产时间线）。(c) 全球化布局期（2021至今）：搜集海外投资公告（匈牙利基地）、国际主机厂订单数据（特斯拉占比变化）。",
        "定量分析当前格局：获取近3年财报核心指标（营收增长率、研发投入占比、动力电池全球市占率）、主要产品参数（钠电池能量密度、麒麟电池量产进度）。"

    ]

    with open("research_report.json", "r", encoding="utf-8") as f:
        research_report = f.read()
        research_report = json.loads(research_report)

    with open("retriever_search_results.json", "r", encoding="utf-8") as f:
        retriever_search_results = f.read()
        retriever_search_results = json.loads(retriever_search_results)

    with open("tavily_search_results.json", "r", encoding="utf-8") as f:
        tavily_search_results = f.read()
        tavily_search_results = json.loads(tavily_search_results)

    async def main():
        async def make_sub_report(sub_plan, research_report, results):
            agent = SubReporterAgent(sub_plan=sub_plan, research_report=research_report)
            messages: Sequence[BaseChatMessage] = [
                TextMessage(source="user", content=json.dumps(results, ensure_ascii=False)),
            ]
            cancellation_token = CancellationToken()

            response = await agent.on_messages(messages, cancellation_token=cancellation_token)
            return response.chat_message.content

        tasks = []
        for i, sub_report in enumerate(research_report):
            results = retriever_search_results[i] + tavily_search_results[i]
            tasks.append(make_sub_report(sub_plan=plans[i], research_report=sub_report, results=results))

        ans = await asyncio.gather(*tasks, return_exceptions=False)

        agent = ReporterAgent(
            user="宁德时代的发展历史",
            list_of_sub_reports=ans
        )
        messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content="现在，请以首席分析师的身份，开始执行你的综合分析任务。"),
        ]
        cancellation_token = CancellationToken()

        response = await agent.on_messages(messages, cancellation_token=cancellation_token)
        print(response.chat_message.content)
        return response.chat_message.content


    asyncio.run(main())