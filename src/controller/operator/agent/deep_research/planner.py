from typing import Annotated, List

from autogen_core.models import ChatCompletionClient
from pydantic import BaseModel, Field

from controller.engine import deepseek_reasoner, deepseek_chat, gemini_2_5_pro
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.tool.time import get_current_time
from controller.operator.prompt.deep_research.planner import planner_prompt, structure_plan_prompt


class Plan(BaseModel):
    description: Annotated[str, Field(title="研究计划描述")]
    stage: Annotated[str, Field(title="完成状态：todo/doing/done", default="todo")] = "todo"


class ResearchPlan(BaseModel):
    plan: Annotated[List[Plan], Field(title="研究计划列表")] = []


class PlannerAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_reasoner, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="planner",
            model_client=model_client,
            token_consumption=token_consumption,
            description="A planner agent that creates detailed research plans for deep research tasks.",
            system_message=planner_prompt.format(current_time=get_current_time())
        )


class StructurePlanAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_chat, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="structure_plan",
            model_client=model_client,
            token_consumption=token_consumption,
            description="A structure plan agent that structures the research plan into a detailed format.",
            system_message=structure_plan_prompt
        )


if __name__ == '__main__':
    import asyncio
    from typing import Sequence, Annotated

    from autogen_agentchat.base import Response
    from autogen_agentchat.messages import TextMessage, BaseChatMessage, ToolCallExecutionEvent, ToolCallRequestEvent, ThoughtEvent
    from autogen_core import CancellationToken

    agent = PlannerAgent()
    messages: Sequence[BaseChatMessage] = [
        TextMessage(source="user", content="特斯拉的发展历程和未来展望"),
    ]
    cancellation_token = CancellationToken()

    async def get_streaming_response():
        async for message in agent.on_messages_stream(messages, cancellation_token):
            if isinstance(message, TextMessage):
                print("text:", message.content)
            elif isinstance(message, ToolCallRequestEvent):
                print("tool call request:", message)
            elif isinstance(message, ToolCallExecutionEvent):
                print("tool call execution:", message)
            elif isinstance(message, ThoughtEvent):
                print("thought:", message.content)
            elif isinstance(message, Response):
                print("response:", message)
            else:
                print("unknown message type:", message)

    response = asyncio.run(get_streaming_response())
