import json

from autogen_core.models import Chat<PERSON>ompletionClient

from controller.engine import gpt_41, gemini_2_5_flash
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.tool.retriever import retriever_search
from controller.operator.tool.tavily_tool import tavily_search
from controller.operator.tool.time import get_current_time
from controller.operator.prompt.deep_research.researcher import researcher_prompt, researcher_summary_prompt, researcher_reflect_prompt


class ResearchAgent(BaseAgent):
    def __init__(self, user: str = None, sub_plan: str = None, model_client: ChatCompletionClient = gpt_41, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="research",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_prompt.format(
                current_time=get_current_time(),
                sub_plan=sub_plan,
                user=user,
            ),
            tools=[retriever_search],
            reflect_on_tool_use=False
        )


class ResearchSummaryAgent(BaseAgent):
    def __init__(self, user: str = None, sub_plan: str = None, model_client: ChatCompletionClient = gpt_41, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="research_summary",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_summary_prompt.format(
                current_time=get_current_time(),
                sub_plan=sub_plan,
                user=user,
            ),
        )


class ResearcherReflectAgent(BaseAgent):
    def __init__(self, user: str = None, sub_plan: str = None, model_client: ChatCompletionClient = gemini_2_5_flash, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="researcher_reflect",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_reflect_prompt.format(
                current_time=get_current_time(),
                sub_plan=sub_plan,
                user=user,
            ),
        )


if __name__ == '__main__':
    import asyncio

    from autogen_agentchat.base import Response, TaskResult
    from autogen_agentchat.conditions import TextMentionTermination
    from autogen_agentchat.teams import RoundRobinGroupChat
    from autogen_agentchat.messages import TextMessage, ThoughtEvent, ToolCallExecutionEvent, ToolCallRequestEvent, ToolCallSummaryMessage

    sub = "检索宁德时代官方年报、招股书及公司官网，提取基础信息：成立时间、创始人背景、注册地、股权结构等基础档案。"

    async def main():
        researcher_agent = ResearchAgent()
        researcher_summary_agent = ResearchSummaryAgent()
        researcher_reflect_agent = ResearcherReflectAgent(sub_plan=sub)

        text_termination = TextMentionTermination("APPROVE")

        team = RoundRobinGroupChat(participants=[researcher_agent, researcher_summary_agent, researcher_reflect_agent],
                                   termination_condition=text_termination)

        async for message in team.run_stream(task=sub):  # type: ignore
            if isinstance(message, TextMessage):
                print("-- content message --")
                print(message.source)
                print(message.content)
            elif isinstance(message, ToolCallRequestEvent):
                print("-- tool call request message --")
                print(message.content[0].name)
                print(message.content[0].arguments)
            elif isinstance(message, ToolCallExecutionEvent):
                print("-- tool call execution message --")
                print(message.content[0].name)
                print(json.loads(message.content[0].content))
            elif isinstance(message, Response):
                print("-- response message from research_team--")
                print(message)

    asyncio.run(main())
