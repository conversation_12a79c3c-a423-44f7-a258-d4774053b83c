import asyncio
import time
import logging
import warnings
from typing import List, Sequence, Any, Callable, Awaitable, Dict

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import Response, Handoff
from autogen_agentchat.messages import BaseChatMessage
from autogen_core import CancellationToken
from autogen_core.memory import Memory
from autogen_core.model_context import Chat<PERSON>ompletionContext
from autogen_core.models import ChatCompletionClient
from autogen_core.tools import BaseTool, Workbench
from pydantic import BaseModel

from common.logger import logger
from controller.operator.runner.base import TokenConsumption

warnings.filterwarnings("ignore", category=UserWarning)

autogen_logger = logging.getLogger("autogen_core.events")
autogen_logger.setLevel(logging.WARNING)


class BaseAgent(AssistantAgent):
    def __init__(
            self,
            name: str,
            model_client: ChatCompletionClient,
            token_consumption: TokenConsumption | None = None,
            *,
            tools: List[BaseTool[Any, Any] | Callable[..., Any] | Callable[..., Awaitable[Any]]] | None = None,
            workbench: Workbench | None = None,
            handoffs: List[Handoff | str] | None = None,
            model_context: ChatCompletionContext | None = None,
            description: str = "An agent that provides assistance with ability to use tool.",
            system_message: (
                    str | None
            ) = "You are a helpful AI assistant. Solve tasks using your tool. Reply with TERMINATE when the task has been completed.",
            model_client_stream: bool = False,
            reflect_on_tool_use: bool | None = None,
            tool_call_summary_format: str = "{result}",
            output_content_type: type[BaseModel] | None = None,
            output_content_type_format: str | None = None,
            memory: Sequence[Memory] | None = None,
            metadata: Dict[str, str] | None = None,
    ):
        super().__init__(name, model_client, tools=tools, workbench=workbench,
                         handoffs=handoffs, model_context=model_context, description=description,
                         system_message=system_message, model_client_stream=model_client_stream,
                         reflect_on_tool_use=reflect_on_tool_use, tool_call_summary_format=tool_call_summary_format,
                         output_content_type=output_content_type, output_content_type_format=output_content_type_format,
                         memory=memory, metadata=metadata)
        self.token_consumption = token_consumption if token_consumption else TokenConsumption()
        self.input_token = 0
        self.output_token = 0

    async def on_messages(self, messages: Sequence[BaseChatMessage], cancellation_token: CancellationToken) -> Response | None:
        task = asyncio.current_task()
        start_time = time.time()

        async for message in self.on_messages_stream(messages, cancellation_token):

            if isinstance(message, Response):
                self.input_token += message.chat_message.models_usage.prompt_tokens
                self.output_token += message.chat_message.models_usage.completion_tokens

                self.token_consumption.input_token.append(self.input_token)
                self.token_consumption.output_token.append(self.output_token)
                logger.info(
                    f"Agent: {self.__class__.__name__} {task.get_name()} is success after {time.time() - start_time:.2f} seconds. "
                    f"Input tokens: {self.input_token}, Output tokens: {self.output_token}.")

            return message

        return None
