from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.doc_extract import custom_extract_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class CustomExtract(BaseRunner):
    def __init__(self, content: str, user_prompt: str, model_name: LLMModel = LLMModel.DEEPSEEK_CHAT,
                    token_consumption: TokenConsumption | None = None):
            super().__init__()

            self.content = content
            self.user_prompt = user_prompt
            self.token_consumption = token_consumption or TokenConsumption()
            self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        response = await self.model_engine.generator(
            system_prompt=custom_extract_prompt.format(user_prompt=self.user_prompt),
            prompt=f"{self.content}",
        )
        content = response.choices[0].message.content.strip()
        return content


if __name__ == '__main__':
    import asyncio

    async def main():
        from controller.operator.example_text import short_text

        results = await asyncio.gather(*[
            CustomExtract(content=short_text, user_prompt="提取出文本中的所有人名", model_name=LLMModel.QWEN3_32B).run()
            for _ in range(1)
        ])
        for content in results:
            print("--" * 20)
            print(content)

    asyncio.run(main())
