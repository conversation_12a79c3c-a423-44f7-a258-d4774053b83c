from pydantic import BaseModel, <PERSON>
from typing import Annotated, Optional

from config import LLMModel
from controller.engine import Chat<PERSON><PERSON><PERSON>
from controller.operator.prompt.doc_extract import sentiment_analysis_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool import JsonifyAgent


class SentimentAnalysisModel(BaseModel):
    BP: Annotated[float, Field(title="基础情绪取向")]
    IA: Annotated[float, Field(title="情感强度")]
    CA: Annotated[float, Field(title="信息可信度")]
    SF: Annotated[float, Field(title="舆情影响指数: SF = (BP + IA) * CA, 超出±100 取对应边界，保留2位小数")]
    extra: Annotated[Optional[str], Field(title="保留字段，无需提取")] = None


class SentimentAnalysis(BaseRunner):
    def __init__(self, content: str, model_name: LLMModel = LLMModel.DEEPSEEK_CHAT,
                 token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content = content
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> SentimentAnalysisModel:
        thinking_response = await self.model_engine.generator(
            system_prompt=sentiment_analysis_prompt,
            prompt=f"{self.content}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        jsonify_content = self.get_markdown_content(content=thinking_content)
        jsonify_agent = JsonifyAgent(SentimentAnalysisModel, token_consumption=self.token_consumption)
        model: SentimentAnalysisModel = await jsonify_agent.jsonify(content=jsonify_content)
        # 小模型例如 QWEN3_32B 的计算大概率有误，通过代码进行修正
        model.SF = round((model.BP + model.IA) * model.CA, 2)
        model.extra = thinking_content
        return model


if __name__ == '__main__':
    import asyncio

    async def main():
        from controller.operator.example_text import short_text

        results = await asyncio.gather(*[
            SentimentAnalysis(content=short_text, model_name=LLMModel.QWEN3_32B).run()
            for _ in range(1)
        ])
        for model in results:
            print("--" * 20)
            print(model)

    asyncio.run(main())
