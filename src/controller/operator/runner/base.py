import asyncio
import json
import re
import time
import traceback
from abc import abstractmethod
from enum import IntEnum
from typing import Annotated, List

from pydantic import BaseModel, Field

from common.logger import logger
from common.snowflake import Snowflake

import logging

logging.getLogger("httpx").setLevel(logging.WARNING)


class RunnerStatus(IntEnum):
    READY = 0
    RUNNING = 1
    SUCCESS = 2
    FAILED = 3


class TokenConsumption(BaseModel):
    input_token: Annotated[List[int], Field(title="提示token")] = []
    output_token: Annotated[List[int], Field(title="补全token")] = []


class BaseRunner:
    def __init__(self, retry_num: int = 3):
        self.id = Snowflake.get_id()
        self.status = RunnerStatus.READY

        self.failed_num = 0
        self.error_msg = None
        self.response = None
        self.token_consumption = TokenConsumption()

        self.retry_num = retry_num

    async def run(self):
        task = asyncio.current_task()
        start_time = time.time()
        logger.info(f"Runner: {self.__class__.__name__} {task.get_name()}:id:{self.id} is running.")
        self.status = RunnerStatus.RUNNING
        for i in range(self.retry_num):
            try:
                result = await self._run()
                self.status = RunnerStatus.SUCCESS
                self.response = result
                logger.info(f"Runner: {self.__class__.__name__} {task.get_name()}:id:{self.id} is success after {time.time() - start_time:.2f} seconds. "
                             f"Input tokens[total]: {sum(self.token_consumption.input_token)}, Output tokens[total]: {sum(self.token_consumption.output_token)}.")
                return result
            except Exception as e:
                self.failed_num += 1
                self.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
                logger.warning(
                    f"Runner: {self.__class__.__name__} {task.get_name()}:id:{self.id} is failed in {self.failed_num}/{self.retry_num} times. "
                    f"Error: {self.error_msg}")

        self.status = RunnerStatus.FAILED
        logger.error(f"Runner: {self.__class__.__name__} {task.get_name()}:id:{self.id} is failed. "
                      f"Latest error: {self.error_msg}")
        return None

    @abstractmethod
    async def _run(self, *args, **kwargs) -> str:
        return "Success"

    @staticmethod
    def split_thinking_content(content: str) -> tuple[str, str]:
        content = content.strip()

        pattern = re.compile(r'<think>(.*?)</think>', re.DOTALL)
        match = pattern.search(content)
        if match:
            thinking_content = match.group(1).strip()
            content = content.replace(match.group(0), "")
            return thinking_content, content.strip()
        else:
            return "", content.strip()

    @staticmethod
    def get_markdown_content(content: str) -> str:
        pattern = re.compile(r'```markdown(.*?)```', re.DOTALL)
        match = pattern.search(content)
        if match:
            return match.group(1).strip()
        else:
            return content.strip()


if __name__ == '__main__':
    runner = BaseRunner()
    asyncio.run(runner.run())
    print(json.dumps(runner.response, ensure_ascii=False, indent=4))
