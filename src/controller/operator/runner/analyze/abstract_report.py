from typing import List

from config import LLMModel
from controller.engine import Chat<PERSON><PERSON><PERSON>
from controller.operator.prompt.analyze import abstract_report_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class AbstractReport(BaseRunner):
    def __init__(self, content: str, analyze_task_info: str, quantify_task_info: List[str],
                 model_name: LLMModel = LLMModel.DEEPSEEK_CHAT, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content = content
        self.analyze_task_info = analyze_task_info
        self.quantify_task_info = quantify_task_info
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        thinking_response = await self.model_engine.generator(
            system_prompt=abstract_report_prompt.format(
                analyze_task_info=self.analyze_task_info,
                quantify_task_info=self.quantify_task_info,
            ),
            prompt=f"{self.content}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        content = self.get_markdown_content(content=thinking_content)
        return content
