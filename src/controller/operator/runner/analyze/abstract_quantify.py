from pydantic import BaseModel, <PERSON>
from typing import Annotated, List, Optional

from config import LLMModel
from controller.engine import ChatEng<PERSON>
from controller.operator.prompt.analyze import abstract_quantify_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool import JsonifyAgent


class QuantifyIndicatorModel(BaseModel):
    indicator_name: Annotated[str, Field(title="指标名称")]
    indicator_type: Annotated[str, Field(title="指标类型(枚举): number/enum/string")]
    value: Annotated[float | str, Field(title="指标值")]
    thinking: Annotated[str, Field(title="思考过程")]


class QuantifyModel(BaseModel):
    indicators: Annotated[List[QuantifyIndicatorModel], Field(title="指标列表")]
    extra: Annotated[Optional[str], Field(title="保留字段，无需提取")] = None


class AbstractQuantify(BaseRunner):
    def __init__(self, abstract_list: list[str], extra_prompt: str, analyze_task_info: str, quantify_task_info: List[str],
                 model_name: LLMModel = LLMModel.DEEPSEEK_CHAT, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.abstract_list = abstract_list
        self.extra_prompt = extra_prompt
        self.analyze_task_info = analyze_task_info
        self.quantify_task_info = quantify_task_info
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> QuantifyModel:
        thinking_response = await self.model_engine.generator(
            system_prompt=abstract_quantify_prompt.format(
                analyze_task_info=self.analyze_task_info,
                quantify_task_info=self.quantify_task_info,
            ),
            prompt=f"补充信息：{self.extra_prompt}\n\n现有信息：{self.abstract_list}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        jsonify_content = self.get_markdown_content(content=thinking_content)
        jsonify_agent = JsonifyAgent(QuantifyModel, token_consumption=self.token_consumption)
        model: QuantifyModel = await jsonify_agent.jsonify(content=jsonify_content)
        model.extra = thinking_content
        return model
