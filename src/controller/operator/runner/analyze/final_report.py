from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.analyze import final_report_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption


class FinalReport(BaseRunner):
    def __init__(self, abstract_list: list[str], extra_prompt: str, analyze_task_info: str,
                 model_name: LLMModel = LLMModel.GEMINI_2_5_FLASH, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.abstract_list = abstract_list
        self.extra_prompt = extra_prompt
        self.analyze_task_info = analyze_task_info
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> str:
        thinking_response = await self.model_engine.generator(
            system_prompt=final_report_prompt.format(
                analyze_task_info=self.analyze_task_info,
            ),
            prompt=f"补充信息：{self.extra_prompt}\n\n现有信息：{self.abstract_list}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        content = self.get_markdown_content(content=thinking_content)
        return content
