from pydantic import BaseModel, <PERSON>
from typing import Annotated, List, Optional

from config import LLMModel
from controller.engine import Chat<PERSON><PERSON><PERSON>
from controller.operator.prompt.analyze import git_citation_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool import JsonifyAgent


class CitationModel(BaseModel):
    citation_id_list: Annotated[List[int], Field(title="引用文档编号列表")]
    extra: Annotated[Optional[str], Field(title="保留字段，无需提取")] = None


class GetCitation(BaseRunner):
    def __init__(self, content: str, analyze_task_info: str, model_name: LLMModel = LLMModel.DEEPSEEK_CHAT,
                 token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.content = content
        self.analyze_task_info = analyze_task_info
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self) -> CitationModel:
        thinking_response = await self.model_engine.generator(
            system_prompt=git_citation_prompt.format(
                analyze_task_info=self.analyze_task_info,
            ),
            prompt=f"{self.content}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        jsonify_content = self.get_markdown_content(content=thinking_content)
        jsonify_agent = JsonifyAgent(CitationModel, token_consumption=self.token_consumption)
        model: CitationModel = await jsonify_agent.jsonify(content=jsonify_content)
        model.extra = thinking_content
        return model
