from pydantic import BaseModel, <PERSON>
from typing import Annotated, List, Optional

from config import LLMModel
from controller.engine import ChatEngine
from controller.operator.prompt.abstract import abstract_suggest_batch_prompt
from controller.operator.runner.base import BaseRunner, TokenConsumption
from controller.operator.tool import JsonifyAgent


class AbstractSuggestResult(BaseModel):
    abstract_suggest: Annotated[str, Field(title="摘要建议内容")]
    citation_list: Annotated[List[int], Field(title="引用编号列表")]


class AbstractSuggestList(BaseModel):
    abstract_suggest_list: Annotated[List[AbstractSuggestResult], Field(title="摘要建议列表")]
    extra: Annotated[Optional[str], Field(title="保留字段，无需提取")] = None


class AbstractSuggest(BaseRunner):
    def __init__(self, user_prompt: str, chunk: List[str],
                 model_name: LLMModel = LLMModel.DEEPSEEK_REASONER, token_consumption: TokenConsumption | None = None):
        super().__init__()

        self.user_prompt = user_prompt
        self.chunk = chunk
        self.token_consumption = token_consumption or TokenConsumption()
        self.model_engine = ChatEngine(model_name=model_name, token_consumption=self.token_consumption)

    async def _run(self):
        thinking_response = await self.model_engine.generator(
            system_prompt=abstract_suggest_batch_prompt.format(
                user_prompt=self.user_prompt,
            ),
            prompt=f"文档列表：{self.chunk}",
        )
        thinking_content = thinking_response.choices[0].message.content.strip()

        jsonify_content = self.get_markdown_content(content=thinking_content)
        jsonify_agent = JsonifyAgent(AbstractSuggestList, token_consumption=self.token_consumption)
        model: AbstractSuggestList = await jsonify_agent.jsonify(content=jsonify_content)
        model.extra = thinking_content
        return model
