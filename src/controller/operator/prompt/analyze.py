git_citation_prompt = """##金融文档摘要深度理解指令
任务目标：理解金融摘要文档的内容，结合分析方向，获取关键文档信息
分析方向：{analyze_task_info}
任务要求：
1. 用户会提供一份金融文档的摘要内容，摘要内容中包含了引用编号[citation:int]。
2. 请你结合分析方向，仔细分析文档的内容，对于高相关性的内容，或者需要验证的内容，你可以要求我提供引用原文信息。
3. 你请求获取的引用文档数量不应该超过 30 篇。
4. 你应该先进行尽可能详细的思考，然后再返回结果。
---
###输出标准：
思考过程：xxx

引用文档列表：
```markdown
"citation_id_list": [111, 222]
```
"""

abstract_report_prompt = """##金融文档摘要详细解读生成指令
任务目标：生成金融文档的详细解读
任务要求：
1. 用户会提供一份金融文档的摘要内容，请你对该摘要内容进行全面的详细解读，你要尽可能的避免信息的损失。
2. 分析方向与定量分析方向是你在详细解读过程中需要重点解读与展开的内容，请务必注意，你的任务不是进行分析，而是对文档的进行详细解读。
3. 你应该先进行尽可能详细的思考，然后再返回最终的解读报告。
---
分析方向：{analyze_task_info}
定量分析方向：{quantify_task_info}
---
###输出标准：
思考过程：xxx

解读报告：
```markdown
xxx
```
"""

abstract_quantify_prompt = """##金融文档量化分析指令
任务目标：根据现有信息与补充信息，结合分析方向，获取定量指标
任务要求：
1. 用户会提供一份金融文档的摘要内容，请你对该摘要内容进行定量分析，获取相关的定量指标。
2. 你应该逐个分析文档中的定量指标，并给出相应的量化结果。
3. 你应该先进行尽可能详细的思考，然后再给出最终的量化结果。
分析方向：{analyze_task_info}
量化标准：{quantify_task_info}
---
你应该先进行尽可能详细的思考，然后再给出最终的量化结果。
---
###输出标准：
思考过程：xxx

量化结果：
```markdown
## 量化指标1: xxx
量化结果：
思考过程：

## 量化指标2: xxx
量化结果：
思考过程：

```
"""

final_report_prompt = """##金融文档分析指令
任务目标：根据现有信息与补充信息，结合分析方向，生成完整的分析报告
分析方向：{analyze_task_info}
---
你应该先进行尽可能详细的思考，然后再返回最终的分析报告。
---
###输出标准：
思考过程：xxx

分析报告：
```markdown
xxx
```
"""
