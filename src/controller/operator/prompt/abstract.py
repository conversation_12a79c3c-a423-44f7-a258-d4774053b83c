abstract_suggest_batch_prompt = """##金融文档集合深度理解与摘要指令
任务目标：对多篇文档进行深度理解，并生成摘要提示。
摘要方向：{user_prompt}
任务要求：
1. 用户会提供一份文档列表，每篇文档包含正文(content)以及引用编号(citation)。
2. 请你经过深度思考，从多个角度进行分析，结合摘要方向，对文档集合给出若干深度理解后的摘要提示，每个摘要提示需要包含完整的事件与前因后果。
3. 不是每篇文档都需要生成摘要提示，只有在你认为有必要的情况下，才需要为该文档生成摘要提示。
4. 你给出的摘要提示中，不仅要包含单篇重点文档的信息，还要充分考虑文档之间的关联性和引用关系，确保生成的摘要提示能够准确反映文档集合的整体内容和主题。
5. 对于每条摘要提示(abstract_suggest)，在其引用编号列表中(citation_list)给出对应的引用编号(citation)，请务必确保引用编号的准确性。
---
###输出标准：
思考过程：xxx

摘要提示列表：
```markdown
## 摘要提示1: xxx
引用编号列表: [111]

## 摘要提示2: xxx
引用编号列表: [222, 333]
```
"""

abstract_generate_prompt = """##文档集合摘要生成指令
任务目标：生成多篇文档的完整摘要。
摘要方向：{user_prompt}
任务要求：
1. 用户会提供一份文档列表。
2. 请你经过深度思考，充分利用文档并抽取重要信息，结合摘要方向，对摘要列表给出最终的汇总摘要，确保生成的摘要能够准确反映文档集合的整体内容和主题。你的创作篇幅需要尽可能延长，对于每一个要点的论述要推测用户的意图，给出尽可能多角度的回答要点，且务必信息量大、论述详尽、有理有据。
3. 如果摘要内容很长，请尽量结构化、分段落总结，合并相关主题的内容，同时选择合适、美观的回答格式，确保可读性强。
4. 在最终摘要中，对于客观数据、重要结论等信息，请使用“该文档”/“文档的具体名称”来指代该文档；并在最终摘要的段落中引用对应的摘要编号(suggest_index)，例如[suggest:3][suggest:5]。
5. 如果文档的内容中包含了类似"citation 270"、"文档23"等表述，请你务必在最终摘要中将他们替换为改摘要对应的摘要编号(suggest_index)，例如[suggest:270]、[suggest:23]。
---
在用户提供了文档信息后，请你从客观角度直接输出你的 markdown 格式的摘要结果，不要输出思考过程、任务要求或其他内容：
"""

abstract_combine_prompt = """##文档集合摘要生成指令
任务目标：生成多篇文档的完整摘要。
摘要方向：{user_prompt}
任务要求：
1. 用户会提供一份文档列表，这些文档是针对同一方向下，不同的文档集合生成的。
2. 请你充分利用这些文档，选择合适文档结构，合并相关主题的内容，确保生成的最终摘要能够准确反映这些文档的整体内容和主题，同时选择合适、美观的回答格式，确保可读性强。
3. 在生成摘要时，请尽量保留段落中的文档编号，例如[suggest:3][suggest:5]，可以使用“该文档”/“文档的具体名称”来指代该文档。
---
在用户提供了文档信息后，请你从客观角度直接输出你的 markdown 格式的摘要结果，不要输出思考过程、任务要求或其他内容：
"""

suggest_format_fix_prompt = """##引用格式修正指令
任务目标：修正引用格式，确保引用编号的格式完全统一。
任务要求：
1. 用户会提供一份文本内容，文本中包含了引用编号。
2. 引用编号的标准格式为"[suggest:1]"，即使用方括号包裹的"suggest"和数字，数字部分可以是任意正整数。
3. 请你检查文本中的引用编号，确保它们符合标准格式，并进行必要的修正，尤其注意括号的使用，修正时请保持文本的原意和上下文的连贯性。
4. 引用编号的格式应该仅包括"[suggest:1]"，如果文本中包含了类似"citation 270"、"文档23"等表述，请你务必帮忙删除它们，同时注意保持文本的完整性和逻辑性。
5. 如果摘要的开头出现了与正文无关的思考过程、任务要求或其他内容，请你将其删除，确保输出的文本仅包含摘要正文。
---
请你直接输出你的修正后的文本，不要输出其他内容：
"""