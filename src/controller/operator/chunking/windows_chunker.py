from .base import BaseChunking, DocModel, ChunkModel
from config import DEFAULT_WINDOWS_CHUNK_SIZE, DEFAULT_WINDOWS_OVERLAP_SIZE


class SlidingWindowChunking(BaseChunking):
    def __init__(self, chunk_size: int = DEFAULT_WINDOWS_CHUNK_SIZE, overlap_size: int = DEFAULT_WINDOWS_OVERLAP_SIZE):
        """
        初始化滑动窗口分块器

        Args:
            chunk_size: 每个块的大小（字符数）
            overlap_size: 相邻块之间的重叠大小（字符数）
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size

        # 确保参数有效
        if chunk_size <= 0:
            raise ValueError(f"chunk_size {chunk_size} must be positive")
        if overlap_size < 0:
            raise ValueError(f"overlap_size {overlap_size} must be non-negative")
        if overlap_size >= chunk_size:
            raise ValueError(f"overlap_size {overlap_size} must be less than chunk_size {chunk_size}")

    def chunk(self, content: str, title: str = None, data_time: str = None) -> DocModel:
        """
        使用滑动窗口方法对文本进行分块

        Args:
            content: 要分块的完整文本
            title: 文章标题
            data_time: 发布时间

        Returns:
            DocModel: 包含完整文本和分块列表的模型
        """
        if not content:
            return DocModel(content="", chunk_children=[])

        chunk_children = []
        text_length = len(content)
        stride = self.chunk_size - self.overlap_size

        # 遍历文本，每次移动stride个字符
        start_offset = 0
        index = 0

        while start_offset < text_length:
            end_offset = min(start_offset + self.chunk_size, text_length)
            chunk_text = content[start_offset:end_offset]

            chunk = ChunkModel(
                index=index,
                chunk_text=chunk_text,
                start_offset=start_offset,
                end_offset=end_offset
            )
            chunk_children.append(chunk)

            # 如果已经处理到文本末尾，就停止
            if end_offset == text_length:
                break

            # 更新下一个窗口的起始位置
            start_offset += stride
            index += 1

        return DocModel(content=content, chunk_children=chunk_children, title=title, data_time=data_time)
