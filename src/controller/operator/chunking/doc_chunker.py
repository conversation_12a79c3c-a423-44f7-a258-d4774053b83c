from typing import List

from .base import BaseChunking, DocModel
from config import DEFAULT_DOC_CHUNK_SIZE


class DocChunking(BaseChunking):
    """
    文档分块器
    """

    def __init__(self, chunk_size: int = DEFAULT_DOC_CHUNK_SIZE):
        """
        初始化文档分块器

        Args:
            chunk_size: 每个块的大小（字符数）
        """
        self.chunk_size = chunk_size

        # 确保参数有效
        if chunk_size <= 0:
            raise ValueError(f"chunk_size {chunk_size} must be positive")

    def chunk(self, doc_list: List[DocModel]) -> List[List[str]]:
        """
        使用文档分块方法对文本进行分块

        Args:
            doc_list: 要分块的文档列表
        """
        chunk_result = []
        current_chunk = []
        current_length = 0

        for index, doc in enumerate(doc_list):
            # 使用摘要内容（如果存在）以降低成本，否则使用原始内容
            content = doc.extract_result.abstract if doc.extract_result else doc.content
            if len(content) > self.chunk_size * 2:
                raise ValueError(
                    f"doc_content length {len(content)} must be less than chunk_size {self.chunk_size * 2}")

            # 如果当前块加上新的内容超过限制，则保存当前块并重置
            if current_chunk and (current_length + len(content) > self.chunk_size):
                chunk_result.append(current_chunk)
                current_chunk = []
                current_length = 0

            current_chunk.append({"citation": index, "content": content})
            current_length += len(content)

        if current_chunk:
            chunk_result.append(current_chunk)

        return chunk_result
