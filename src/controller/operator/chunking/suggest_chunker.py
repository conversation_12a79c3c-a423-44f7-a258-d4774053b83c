from typing import List

from .base import BaseChunking, DocModel
from config import DEFAULT_ABSTRACT_CHUNK_SIZE, DEFAULT_ABSTRACT_OVERLAP_SIZE


class SuggestChunking(BaseChunking):
    """
    文档分块器
    """

    def __init__(self, chunk_size: int = DEFAULT_ABSTRACT_CHUNK_SIZE, overlap_size: int = DEFAULT_ABSTRACT_OVERLAP_SIZE):
        """
        初始化文档分块器

        Args:
            chunk_size: 每个块的大小（字符数）
            overlap_size: 重叠大小（字符数）
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size

        # 确保参数有效
        if chunk_size <= 0:
            raise ValueError(f"chunk_size {chunk_size} must be positive")
        if overlap_size < 0:
            raise ValueError(f"overlap_size {overlap_size} must be non-negative")
        if overlap_size >= chunk_size:
            raise ValueError(f"overlap_size {overlap_size} must be less than chunk_size {chunk_size}")

    def chunk(self, suggest_list: List[dict]) -> List[List[dict]]:
        """
        使用文档分块方法对文本进行分块

        Args:
            suggest_list: 要分块的文档列表
                abstract_suggest: 摘要建议
                suggest_index: 摘要编号
        """
        if not suggest_list:
            return []

            # 预计算前缀字符数列表，prefix[i] 表示 0 到 i-1 号建议的 "abstract_suggest" 总字符数
        prefix = [0]
        for suggestion in suggest_list:
            text = suggestion.get("abstract_suggest", "")
            prefix.append(prefix[-1] + len(text))

        n = len(suggest_list)
        chunks = []
        i = 0
        while i < n:
            # 找到 j，使得从 i 到 j 的总字符数小于等于 chunk_size
            j = i + 1
            # 至少包含一个建议，即使该建议本身超过 chunk_size
            while j <= n and prefix[j] - prefix[i] <= self.chunk_size:
                j += 1
            # 回退一步，确保 j 指向满足条件的子列表末尾
            j -= 1

            # 确保至少包含一项。如果单个建议超过chunk_size则 j==i
            if j == i:
                j = i + 1

            # 当前块
            current_chunk = suggest_list[i:j]
            chunks.append(current_chunk)

            # 如果已经处理完所有建议，则退出
            if j >= n:
                break

            # 为实现重叠效果，从当前块末尾反向确定新的起始索引 new_i，以确保从 new_i 到 j 的字符数至少达到 overlap_size
            new_i = i  # 默认情况下，如果无法满足重叠，则下次从 j 开始
            for k in range(i, j):
                if prefix[j] - prefix[k] >= self.overlap_size:
                    new_i = k
                    break
            # 下一块起始位置为 new_i，如果 new_i 不前移，则直接从 j 开始
            i = new_i if new_i > i else j

        return chunks

