#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum

from sqlalchemy import select, func, Integer

from engine.rdb import g, fetch_one, fetch_all
from model.token_counts import TokenCountsModel
from controller.admin import Tenant
from exception import TenantTokensNotEnoughError


class LLMBusiness(StrEnum):
    doc_extract = "doc_extract"
    abstract = "abstract"
    analytics = "analytics"
    deep_research = "deep_research"
    chat = "chat"
    web_search = "web_search"
    compare = "compare"


BusinessNameMapping = {
    LLMBusiness.doc_extract: "文档分析",
    LLMBusiness.abstract: "摘要总结",
    LLMBusiness.analytics: "量化跟踪",
    LLMBusiness.deep_research: "深度研究",
    LLMBusiness.chat: "问答对话",
    LLMBusiness.web_search: "网络搜索",
    LLMBusiness.compare: "数据对比"
}


class TokenCountsController:
    @staticmethod
    def get_query(start: str = None, end: str = None, business: str = None, create_user_id: int = None):
        where = [TokenCountsModel.is_delete == 0]
        if g.tenant_id:
            where.append(TokenCountsModel.tenant_id == g.tenant_id)
        if start is not None:
            where.append(TokenCountsModel.create_time >= start)
        if end is not None:
            where.append(TokenCountsModel.create_time < end)
        if business is not None:
            where.append(TokenCountsModel.business == business)
        if create_user_id is not None:
            where.append(TokenCountsModel.create_user_id == create_user_id)

        query = (
            select(
                func.coalesce(func.sum(TokenCountsModel.input_tokens), 0).cast(Integer).label("input_tokens"),
                func.coalesce(func.sum(TokenCountsModel.output_tokens), 0).cast(Integer).label("output_tokens"))
            .where(*where))

        return query

    async def get_one(self, start: str = None, end: str = None, business: str = None, create_user_id: int = None):
        query = self.get_query(start=start, end=end, business=business, create_user_id=create_user_id)
        return await fetch_one(query)

    @staticmethod
    async def get_business_stats(tenant_id: None = None, start: str = None, end: str = None):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id, "租户ID禁止为空"

        where = [TokenCountsModel.tenant_id == tenant_id, TokenCountsModel.is_delete == 0]
        if start is not None:
            where.append(TokenCountsModel.create_time >= start)
        if end is not None:
            where.append(TokenCountsModel.create_time < end)

        query = (
            select(
                TokenCountsModel.business,
                func.sum(TokenCountsModel.input_tokens + TokenCountsModel.output_tokens).cast(Integer).label("total_tokens"))
            .where(*where)
            .group_by(TokenCountsModel.business)
        )
        business_tokens_stats = await fetch_all(query)

        stats = []
        for bts in business_tokens_stats:
            stats.append({
                "name": BusinessNameMapping.get(bts["business"], bts["business"]),
                "value": bts["total_tokens"]
            })

        return stats

    @staticmethod
    async def check_plan(tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id, "租户ID禁止为空"

        billable_plan = await Tenant.get_billable_one(tenant_id=tenant_id)
        if not billable_plan:
            raise TenantTokensNotEnoughError

    async def record(self, create_user_id: int, model_name: str, business: LLMBusiness, input_tokens: int,
                     output_tokens: int, tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        assert tenant_id, "租户ID禁止为空"

        await self.create(
            create_user_id=create_user_id, model_name=model_name, business=business, input_tokens=input_tokens,
            output_tokens=output_tokens, tenant_id=tenant_id)

        billable_plan = await Tenant.get_billable_one(tenant_id=tenant_id)
        if billable_plan:
            # 如果没有有效订阅套餐,允许跑完流程,下次由check_plan完成检查
            await Tenant.update_plan(
                tenant_plan_id=billable_plan["tenant_plan_id"],
                used_add=input_tokens + output_tokens)


    @staticmethod
    async def create(create_user_id: int, model_name: str, business: LLMBusiness, input_tokens: int, output_tokens: int,
                     tenant_id: int = None):
        tenant_id = tenant_id or g.tenant_id
        token_counts = TokenCountsModel(
            create_user_id=create_user_id, tenant_id=tenant_id, model_name=model_name, business=business,
            input_tokens=input_tokens, output_tokens=output_tokens)
        g.session.add(token_counts)
        await g.session.flush()

        return token_counts.id


TokenCounts = TokenCountsController()
