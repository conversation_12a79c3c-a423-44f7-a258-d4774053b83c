#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import select, update

from engine.rdb import g, session_maker_sync, paginator, query_order, fetch_one
from model.plan import PlanModel, PlanType, RefreshType


class PlanController:
    @staticmethod
    def get_query(plan_id: int = None, plan_type: PlanType = None, order_by: str = None):
        where = [PlanModel.is_delete == 0]
        if plan_id is not None:
            where.append(PlanModel.id == plan_id)
        if plan_type is not None:
            where.append(PlanModel.plan_type == plan_type)

        query = (
            select(
                PlanModel.id.label("plan_id"),
                PlanModel.name,
                PlanModel.tokens,
                PlanModel.refresh_type,
                PlanModel.refresh_period_days,
                PlanModel.refresh_count
            )
            .where(*where)
        )

        query = query_order(query=query, order_by=order_by, table=PlanModel)

        return query

    async def get_tenant_one(self, plan_id: int):
        query = self.get_query(plan_id=plan_id, plan_type=PlanType.tenant)
        return await fetch_one(query=query)

    async def get_tenant_list(self, page: int = 1, per_page: int = 20, order_by: str = None):
        query = self.get_query(plan_type=PlanType.tenant, order_by=order_by)
        pager, items = await paginator(query, page=page, per_page=per_page)
        return pager, items

    @staticmethod
    async def create_tenant(name: str, tokens: int, refresh_type: RefreshType, refresh_period_days: int = None,
                            refresh_count: int = None):
        plan = PlanModel(
            name=name, tokens=tokens, refresh_type=refresh_type, refresh_period_days=refresh_period_days,
            refresh_count=refresh_count, plan_type=PlanType.tenant)
        g.session.add(plan)
        await g.session.flush()

        return plan.id

    @staticmethod
    async def update_tenant(plan_id: int, name: str = None, tokens: int = None, refresh_period_days: int = None,
                            refresh_count: int = None):
        update_info = {}
        if name is not None:
            update_info[PlanModel.name] = name
        if tokens is not None:
            update_info[PlanModel.tokens] = tokens
        if refresh_period_days is not None:
            update_info[PlanModel.refresh_period_days] = refresh_period_days
        if refresh_count is None:
            update_info[PlanModel.refresh_count] = refresh_count

        query = (update(PlanModel)
                 .where(PlanModel.id == plan_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete_tenant(plan_id: int):
        query = (update(PlanModel)
                 .where(PlanModel.id == plan_id)
                 .values({PlanModel.is_delete: 1}))
        await g.session.execute(query)

    @staticmethod
    def init_sync():
        with session_maker_sync() as session:
            plan = PlanModel(
                id=1, plan_type=PlanType.tenant, name="默认租户-无限Token", tokens=0, refresh_type=RefreshType.one_time)
            session.add(plan)
            session.commit()


Plan = PlanController()
