#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import List
from sqlalchemy import select, or_

from engine.rdb import g, fetch_all, session_maker_sync
from model.auth import RoleModel, RoleType


class RoleController:
    @staticmethod
    def get_query(role_id: int = None, role_ids: List[int] = None, types: List[RoleType] = None):
        where = [RoleModel.is_delete == 0]
        if g.tenant_id is not None:
            where.append(
                or_(
                    RoleModel.tenant_id == g.tenant_id, RoleModel.tenant_id == 0
                )
            )
        if role_id is not None:
            where.append(RoleModel.id == role_id)
        if role_ids is not None:
            where.append(RoleModel.id.in_(role_ids))
        if types is not None:
            where.append(RoleModel.type_.in_(types))

        query = (
            select(
                RoleModel.id.label("role_id"),
                RoleModel.name.label("role_name"),
                RoleModel.type_.label("role_type")
            )
            .where(*where)
        )
        return query

    async def get_all(self, role_ids: List[int] = None, types: List[RoleType] = None):
        query = self.get_query(role_ids=role_ids, types=types)
        return await fetch_all(query=query)

    @staticmethod
    def get_default_roles(is_super_admin: bool = False):
        default_roles = [
            {
                "role_id": -1,
                "role_name": "超级管理员",
                "role_type": RoleType.super_admin
            },
            {
                "role_id": -2,
                "role_name": "租户管理员",
                "role_type": RoleType.tenant_admin
            },
            {
                "role_id": -3,
                "role_name": "租户用户",
                "role_type": RoleType.tenant_user
            }
        ]
        if not is_super_admin:
            default_roles = [r for r in default_roles if r["role_type"] != RoleType.super_admin]

        return default_roles

    @staticmethod
    async def create(tenant_id: int, name: str, type_: RoleType):
        role = RoleModel(type_=type_, tenant_id=tenant_id, name=name)
        g.session.add(role)
        await g.session.flush()

        return role.id

    def init_sync(self):
        with session_maker_sync() as session:
            for default_role in self.get_default_roles(is_super_admin=True):
                role = RoleModel(id=default_role["role_id"], type_=default_role["role_type"], tenant_id=0, name=default_role["role_name"])
                session.add(role)
            session.commit()


Role = RoleController()
