import logging
import traceback
from datetime import datetime
from enum import StrEnum
from typing import List, Optional, Annotated

from pydantic import BaseModel, Field

from common.time import now_tz_datestring_with_millis
from config import MAX_HISTORY_LEN
from controller.engine import ChatEngine
from controller.chat.session import Session
from model.session import ChatMessage


class StreamStage(StrEnum):
    THINKING = "thinking"
    GENERATING = "generating"
    ERROR = "error"


class CompletionStreamResponse(BaseModel):
    stage: Annotated[StreamStage, Field(title="思考阶段")]
    session_id: Annotated[int, Field(title="会话ID")]
    request_id: Annotated[str, Field(title="请求ID")]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    content: Annotated[Optional[str], Field(title="回复内容")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class ChatHelper:
    def __init__(self, session_id: str, user: str, history: List[ChatMessage] = None):
        self.session_id = session_id
        self.user = user
        self.history = history if history else []
        self.chat_engine = ChatEngine()
        self.chat_message = ChatMessage(
            user=user,
            query_time=now_tz_datestring_with_millis()
        )

    async def generator(self):
        try:
            history = self.history or await self.get_chat_history(session_id=self.session_id)
            response = await self.chat_engine.generator(prompt=self.user, history=history, stream=True)
            assistant = ""
            async for message in response:
                content = message.choices[0].delta.plain_content
                if content:
                    assistant += content
                    yield self.response_chunk(content=content)
            self.chat_message.assistant = assistant

        except Exception as e:
            self.chat_message.error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            logging.error(f"Failed to generate completion stream: {self.chat_message.error_msg}")

        finally:
            logging.info(f"Chat history: {self.chat_message.model_dump_json(indent=4)}")
            await Session.append_to_chat_history(self.session_id, self.chat_message, refresh=True)

        yield "data: [DONE]\n\n"

    @staticmethod
    async def get_chat_history(session_id: str, max_len: int = MAX_HISTORY_LEN) -> List[ChatMessage]:
        session = await Session.get_es_one(session_id)
        return session.chat_history[-max_len:]

    def response_chunk(self, error_msg: str = None, content: str = None, stage: StreamStage = StreamStage.GENERATING) -> str:
        chunk = CompletionStreamResponse(
            stage=stage,
            error_msg=error_msg,
            session_id=self.session_id,
            request_id=self.chat_message.request_id,
            create_time=now_tz_datestring_with_millis(),
            content=content,
        )
        return f"data: {chunk.model_dump_json()}\n\n"
