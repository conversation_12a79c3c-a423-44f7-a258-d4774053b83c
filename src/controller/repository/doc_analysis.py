from engine.es import es
from model.doc import ALL_REPO_INDEX


class DocAnalysisController:
    @staticmethod
    async def get_all():
        code = ["stock_news_main_cx"]
        opinion_datas = []
        search_after = None
        pit = await es.open_point_in_time(index=ALL_REPO_INDEX, keep_alive="1m")
        pit_id = pit["id"]
        while True:
            search_result = await es.search(
                query={"bool": {"filter": {"terms": {"code": code}}}},
                size=10000,
                _source_includes=["data_time", "title", "content", "code", "extract_result"],
                sort=[{"data_time": "asc"}],
                pit={"id": pit_id, "keep_alive": "1m"},
                search_after=search_after,
                track_total_hits=False)

            if len(search_result["hits"]["hits"]) > 0:
                search_after = search_result["hits"]["hits"][-1]["sort"]
                pit_id = search_result["pit_id"]
                source_data = [{**doc["_source"], "_id": doc["_id"]} for doc in search_result["hits"]["hits"]]
                opinion_datas.extend(source_data)

            else:
                await es.close_point_in_time(body={"id": pit_id})
                break

        return opinion_datas

    @staticmethod
    async def update(index: str, _id: str, doc: dict):
        await es.update(
            index=index,
            id=_id,
            body={
                "doc": doc
            }
        )


DocAnalysis = DocAnalysisController()
