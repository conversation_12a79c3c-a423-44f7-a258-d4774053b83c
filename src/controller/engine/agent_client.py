from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config import LLM_API_URL, LLM_API_KEY, LLMModel


gpt_41 = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GPT_41,
    api_key=LLM_API_KEY,
    temperature=0,
)

o4_mini = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.O4_MINI,
    api_key=LLM_API_KEY,
)

gemini_2_5_flash_lite = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_FLASH_LITE,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": True,
    },
)

gemini_2_5_flash = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_FLASH,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": True,
    },
)

gemini_2_5_pro = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.GEMINI_2_5_PRO,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": True,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.GEMINI_2_5_PRO,
        "structured_output": True,
    },
)

deepseek_chat = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_CHAT,
    api_key=LLM_API_KEY,
    temperature=0,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": False,
    },
)

deepseek_reasoner = OpenAIChatCompletionClient(
    base_url=LLM_API_URL,
    model=LLMModel.DEEPSEEK_REASONER,
    api_key=LLM_API_KEY,
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.R1,
        "structured_output": False,
    },
)
