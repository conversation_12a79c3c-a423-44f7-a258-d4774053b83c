from typing import List

from openai import Async<PERSON>penA<PERSON>

from config import LLM_API_URL, LLM_API_KEY, EmbeddingModel


EmbeddingModelConnection = AsyncOpenAI(base_url=LLM_API_URL, api_key=LLM_API_KEY)


class EmbeddingEngine:
    def __init__(self, model: EmbeddingModel = EmbeddingModel.BGE_M3):
        self.embedding_model = EmbeddingModelConnection
        self.model_name = model

    async def encode(self, text: List[str]) -> List[List[float]]:
        response = await self.embedding_model.embeddings.create(
            model=self.model_name,
            input=text,
        )
        return [response.data[i].embedding for i in range(len(text))]


if __name__ == '__main__':
    import asyncio
    embedding_engine = EmbeddingEngine()
    res = asyncio.run(embedding_engine.encode(["你好", "欢迎光临"]))
    print(res)
    print(len(res[0]))
