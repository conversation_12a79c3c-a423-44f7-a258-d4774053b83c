import asyncio
from typing import List

from config import ARK_API_KEY, ARK_TIMEOUT

try:
    from volcenginesdkarkruntime import AsyncArk
except ModuleNotFoundError:
    print("volcenginesdkarkruntime 模块未安装，跳过初始化")
    ARK_API_KEY = None




if ARK_API_KEY:
    client = AsyncArk(
        api_key=ARK_API_KEY,
        timeout=ARK_TIMEOUT,
    )


async def batch_chat(messages: List[dict[str: str]], endpoint_id: str):
    request = {
        "model": endpoint_id,
        "messages": messages
    }

    completion = await client.batch_chat.completions.create(**request)

    return completion
