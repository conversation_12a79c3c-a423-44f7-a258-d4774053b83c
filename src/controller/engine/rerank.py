import httpx
from typing import List

from config import LLM_API_KEY, LLM_API_URL, RerankerModel


class RerankEngine:
    def __init__(self, model: RerankerModel = RerankerModel.BGE_RERANKER_V2_M3):
        self.model_name = model

    async def rerank(self, query: str, docs: List[str], top_n: int = None) -> List[dict]:
        """
        Rerank the documents based on the query
        :param query: query string
        :param docs: list of documents
        :param top_n: number of top documents to return, if None, return all documents
        :return: list of reranked documents ordered by relevance score
            e.g. [{"index": 0, "relevance_score": 0.9890090823173523}, ...]
        """
        async with httpx.AsyncClient(transport=httpx.AsyncHTTPTransport(retries=3)) as client:
            response = await client.post(
                f"{LLM_API_URL}/rerank",
                headers={"Authorization": f"Bearer {LLM_API_KEY}"},
                json={
                    "model": self.model_name,
                    "query": query,
                    "top_n": len(docs) if top_n is None else top_n,
                    "documents": docs
                }
            )

        return response.json()["results"]


if __name__ == '__main__':
    import asyncio
    rerank_engine = RerankEngine()
    res = asyncio.run(rerank_engine.rerank("你好", ["很高兴见到你", "欢迎光临", "今天天气怎么样"]))
    print(res)
