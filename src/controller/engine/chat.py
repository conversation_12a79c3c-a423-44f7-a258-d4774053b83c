import asyncio
import time
import uuid
from typing import List

from anthropic import AsyncStream
from deepseek_tokenizer import ds_token as tokenizer
from openai import Async<PERSON>penA<PERSON>
from openai.types.chat import Chat<PERSON>ompletionChunk

from config import LLM_API_URL, LLM_API_KEY, LLMModel
from common.logger import logger
from controller.engine.batch_chat_ark import batch_chat
from controller.operator.runner.base import TokenConsumption
from model.session import ChatMessage


ChatModelConnection = AsyncOpenAI(base_url=LLM_API_URL, api_key=LLM_API_KEY)


class ChatEngine:
    def __init__(self, model_name: str | LLMModel = LLMModel.DEEPSEEK_CHAT, token_consumption: TokenConsumption | None = None,
                 base_url: str = None, api_key: str = None):
        self.chat_model = ChatModelConnection
        self.base_url = base_url
        self.api_key = api_key
        self.model_name = model_name
        self.token_consumption = token_consumption if token_consumption else TokenConsumption()
        self.input_token = 0
        self.output_token = 0

    async def generator(self, prompt: str, system_prompt: str = None, history: List[ChatMessage] = None,
                        stream: bool = False, **kwargs):
        messages, token = [], prompt
        task = asyncio.current_task()
        start_time = time.time()

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
            token += system_prompt

        if history:
            for chat_message in history:
                if chat_message.error_msg is None and chat_message.is_delete is False:
                    if chat_message.user:
                        messages.append({"role": "user", "content": chat_message.rewrite_user or chat_message.user})
                        token += chat_message.user
                    if chat_message.assistant:
                        messages.append({"role": "assistant", "content": chat_message.assistant})
                        token += chat_message.assistant

        if prompt:
            messages.append({"role": "user", "content": prompt})

        if self.base_url:
            chat_model = AsyncOpenAI(base_url=self.base_url, api_key=self.api_key)
        else:
            chat_model = self.chat_model

        self.input_token += sum(len(tokenizer.encode(message["content"])) for message in messages)

        if self.model_name.startswith("ark-"):
            response = await batch_chat(messages=messages, endpoint_id=self.model_name.replace("ark-", ""))

        elif "qwen" in self.model_name:
            response = await chat_model.chat.completions.create(
                model=self.model_name,
                messages=messages,
                stream=stream,
                extra_body={"enable_thinking": False},
                **kwargs
            )

        else:
            response = await chat_model.chat.completions.create(
                model=self.model_name,
                messages=messages,
                stream=stream,
                **kwargs
            )

        if self.base_url:
            await chat_model.close()

        if not stream:
            content = response.choices[0].message.content.strip()
            self.output_token += len(tokenizer.encode(content))
            self.token_consumption.input_token.append(self.input_token)
            self.token_consumption.output_token.append(self.output_token)

            logger.info(
                f"LLM call: {self.__class__.__name__} {task.get_name()} is success after {time.time() - start_time:.2f} seconds. "
                f"Input tokens: {self.input_token}, Output tokens: {self.output_token}.")

        else:
            self.token_consumption.input_token.append(self.input_token)
            async def _stream_wrapper(stream_response: AsyncStream[ChatCompletionChunk]):
                output_tokens_for_call = 0
                try:
                    async for chunk in stream_response:
                        if chunk.choices and chunk.choices[0].delta.content:
                            output_tokens_for_call += len(tokenizer.encode(chunk.choices[0].delta.content))
                        yield chunk
                finally:
                    self.token_consumption.output_token.append(output_tokens_for_call)

            return _stream_wrapper(response)

        return response


if __name__ == '__main__':
    chat_engine = ChatEngine(model_name=LLMModel.QWEN3_30B_INSTRUCT)
    async def test():
        history = [
            ChatMessage(user="说说你的爱好", assistant="我的爱好是钓鱼", request_id=str(uuid.uuid4())),
        ]
        resp = await chat_engine.generator("再说一遍你的爱好？", history=history, stream=True)
        async for message in resp:
            print(message)

    # asyncio.run(test())
    res = asyncio.run(chat_engine.generator("你是谁"))
    print(res)
    print(res.choices[0].message.content.strip())
    print(res.choices[0].message.model_dump().get("reasoning_content", ""))
