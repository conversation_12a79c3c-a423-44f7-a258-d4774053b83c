#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrE<PERSON>
from typing import Annotated, Optional

import httpx
from pydantic import BaseModel, Field

from config import ZHIPU_SEARCH_URL, ZHIPU_APIKEY
from common.logger import logger
from controller.retriever.web.base import WebRetriever, SearchResult


class ZhiPuSearchConfig(BaseModel):
    class SearchRecency(StrEnum):
        oneDay = "day"
        oneWeek = "week"
        oneMonth = "month"
        oneYear = "year"

    extract: Annotated[bool, Field(title="是否进行内容提取")] = False
    domain: Annotated[Optional[str], Field(title="指定域名")] = None
    recency: Annotated[Optional[SearchRecency], Field(title="搜索时效")] = None
    count: Annotated[int, Field(title="返回结果数量", ge=1, le=30)] = 10


class ZhiPuWebRetriever(WebRetriever):
    async def web_search(self):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url=ZHIPU_SEARCH_URL,
                    headers={"Authorization": ZHIPU_APIKEY},
                    json={
                        "search_query": self.query,
                        "search_engine": "search_pro",
                        "search_intent": False,
                        "count": 50,
                        "search_domain_filter": self.search_config.domain,
                        "search_recency": self.search_config.recency if self.search_config.recency else "noLimit",
                        "content_size": "medium",
                        "request_id": self.request_id
                    },
                    timeout=5
                )
            response.raise_for_status()
        except Exception as e:
            logger.error(self.log_prefix + f"[ZhiPu]网络搜索失败: query: {self.query} error: {str(e)}")
            return []

        else:
            results = response.json()["search_result"]
            search_results = [
                SearchResult(
                    title=sr["title"],
                    data_time=sr["publish_date"] if sr["publish_date"] else None,
                    content=sr["content"],
                    icon=sr["icon"] if sr["icon"] else None,
                    source=sr["media"] if sr["media"] else None,
                    url=sr["link"]
            ) for sr in results if sr["link"]]
            logger.info(self.log_prefix + f"[ZhiPu]网络搜索成功: query: {self.query} 找到{len(results)}/{self.search_config.count}个相关结果")

            return search_results[:self.search_config.count]


if __name__ == '__main__':
    import asyncio
    web_retrieve = ZhiPuWebRetriever(ZhiPuSearchConfig(), query="浦银理财是做什么的？")
    res = asyncio.run(web_retrieve.searching())
    print(res)