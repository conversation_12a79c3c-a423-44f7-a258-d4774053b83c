#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum, IntEnum
from typing import Annotated
from pydantic import BaseModel, Field, model_validator

from .base import WebRetriever
from .tvly import TvlyWebRetriever, TvlySearchConfig
from .zhipu import ZhiPuWebRetriever, ZhiPuSearchConfig


class SearchEngine(StrEnum):
    zhipu = "zhipu"
    tavily = "tavily"


class SearchEngineTokens(IntEnum):
    """模型计费"""
    zhipu = 3000
    tavily = 10000

class WebSearchConfig(BaseModel):
    name: Annotated[SearchEngine, Field(title="搜索引擎名称")]
    search_config: Annotated[dict | BaseModel, Field(title="搜索引擎配置")]
    retriever: Annotated[type[WebRetriever], Field(exclude=True, title="搜索引擎实例")] = None

    @model_validator(mode="after")
    def assign_retriever(self):
        if self.name is SearchEngine.zhipu:
            self.search_config = ZhiPuSearchConfig(**self.search_config) if self.search_config else ZhiPuSearchConfig()
            self.retriever = ZhiPuWebRetriever
        if self.name is SearchEngine.tavily:
            self.search_config = TvlySearchConfig(**self.search_config) if self.search_config else TvlySearchConfig()
            self.retriever = TvlyWebRetriever
        return self

