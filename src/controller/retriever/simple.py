#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re
import math
from collections import Counter
from typing import List

from langchain_text_splitters import RecursiveCharacterTextSplitter

from controller.retriever.base import BaseRetriever
from controller.operator.chunking.base import ChunkModel


class SimpleSpliter:
    @staticmethod
    def split_text(text: str, chunk_size: int = 1024) -> List[ChunkModel]:
        # 如果长度本身低于要求,直接返回即可.如果长度高于最大长度要求,按以下逻辑切分
        splitter = RecursiveCharacterTextSplitter(
            separators=["。？！?!，"],
            chunk_size=chunk_size,
            chunk_overlap=0,
            keep_separator=True,
        )

        new_texts = []
        # 1.段落以\n做标准切分
        for paragraph in text.split("\n"):
            if not paragraph.strip():
                continue

            if len(paragraph) <= chunk_size:
                new_texts.append(paragraph)
                continue

            # 2. 如果段落长度超过chunk_size,使用RecursiveCharacterTextSplitter进行切分
            for st in splitter.split_text(text=text):
                # 如果仍未切分,进行强行切分
                if len(st) > chunk_size:
                    for step in range(0, len(st), chunk_size):
                        new_texts.append(st[step:step + chunk_size])
                else:
                    new_texts.extend(splitter.split_text(text=text))

        # 这里采用暴力index逆向,保证不会因为算法错误而导致offset偏移
        chunk_children = []
        start_offset = 0
        for chunk_text in new_texts:
            start_offset = text.index(chunk_text, start_offset)
            chunk_children.append(
                ChunkModel(chunk_text=chunk_text, start_offset=start_offset, end_offset=start_offset+len(chunk_text)))

        return chunk_children


class SimpleRetrieverController(BaseRetriever):
    splitter = SimpleSpliter()

    async def retrieve(self, query: str, text: str, max_length: int = 64000, chunk_size: int = 1024) -> List[ChunkModel]:
        match_tokens = await self.get_query_token_weight_no_rewrite(threshold=0.01)
        chunk_children = self.splitter.split_text(text=text, chunk_size=chunk_size)
        if len(text) <= max_length:
            return chunk_children

        pattern = re.compile("|".join([re.escape(keyword) for keyword in match_tokens.keys()]), re.IGNORECASE)

        indexed_texts = []
        last_score = 0
        for i, chunk in enumerate(chunk_children):
            matches = pattern.findall(chunk.chunk_text)
            score = 0

            # 基础分数: 1 + (频次-1) * 0.2
            for word, count in Counter(matches).items():
                score += 1 + match_tokens[word] * (count - 1) * 0.2
            # 过短的文字给予保底权重,防止关键标题信息丢失
            if len(text) < 20 and len(set(text)) > 1:
                score = max(0.1, score)
            # 使用指数衰减为较短的文字增加权重,尽量避免短文本的劣势
            score *= self.exponential_decay_weight(word_count=len(text))
            # 加和1/3上个段落的分数,避免丢失关键下文
            if last_score:
                score += round(last_score / 3, 2)

            indexed_texts.append({"chunk": chunk, "score": score, "index": i})
            last_score = score

        # 按分数降序排序
        sorted_texts = sorted(indexed_texts, key=lambda x: -x["score"])

        selected = []
        total_length = 0

        # 贪心选择：从高分到低分，直到总长度接近k
        for item in sorted_texts:
            text_length = len(item["chunk"].chunk_text)
            if total_length + text_length <= max_length:
                selected.append(item)
                total_length += text_length
            else:
                break

        # 按原始索引排序恢复顺序
        selected_sorted = sorted(selected, key=lambda x: x["index"])
        chunk_children = [item["chunk"] for item in selected_sorted]

        return chunk_children

    @staticmethod
    def exponential_decay_weight(word_count: int) -> float:
        """
        调整后的指数衰减权重：
        - 字数 < 10   : 权重 = 5
        - 10 ≤ 字数 < 100 : 50字时≈2
        - 字数 ≥ 100  : 权重 = 1
        """
        if word_count < 10:
            return 5.0
        elif word_count >= 100:
            return 1.0
        else:
            # 调整衰减系数k，使得word_count=50时权重≈2
            # 公式推导：2 = 5 * e^(-k * (50-10)) → k = -ln(2/5)/40 ≈ 0.0228
            k = 0.0228
            decayed_weight = 5 * math.exp(-k * (word_count - 10))
            return round(decayed_weight, 2)


SimpleRetriever = SimpleRetrieverController()
