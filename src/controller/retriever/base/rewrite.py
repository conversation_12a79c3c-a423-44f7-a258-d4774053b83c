#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import re

import json_repair

from controller.engine.chat import ChatEngine, LLMModel, ChatMessage


common_prompt = """请处理一个问句扩展任务，目的是在RAG中提供更好的检索效果。

历史问答:
{{history_prompt}}
新问句：
{{query}}

## 任务1 问题改写
将新问句与历史问答合并为语义完整的新问题，并纠正明显的输入错误。记为query_rewrite

## 任务2 关键词、搜索词扩展
根据阶段1结果query_rewrite
1. 提取3个最能代表问句的核心关键词，**仅保留名词**，记为query_keywords
2. 给出3个核心关键词最相关的近义词或相关词，记为query_associative_keywords
3. 从搜索角度触发，构造0-5个关键词、同义词组成的搜索语句。记为query_search_terms
示例：
query_rewrite: 5090和5070Ti算力差距有多少? -> {"query_keywords": ["5090", "5070Ti", "算力"], "query_associative_keywords": ["显卡", "性能"], "search_term": ["5090显卡 性能参数", "5070Ti显卡 性能参数", "5090 vs 5070Ti 性能差距"]}

## 最终输出
按模板输出Json，不要输出其他信息

```json
{"query_rewrite": ...,"query_keywords": ...,"query_associative_keywords": ...,"query_search_terms": ...}
```
/no_think"""


class RewriteEngine:
    def __init__(self, model_name: LLMModel = LLMModel.QWEN3_30B_INSTRUCT):
        self.model_name = model_name
        self.system_prompt = "You are a helpful assistant."
        self.chat_engine = ChatEngine(model_name=model_name)

    async def rewrite(self, query: str, history: list[ChatMessage] = None):
        if history:
            history_prompt = self.history_prompt(history=history)
        else:
            history_prompt = "\n\n"
        rewrite_prompt = common_prompt.replace("{{history_prompt}}", history_prompt)
        rewrite_prompt = rewrite_prompt.replace("{{query}}", query)

        res = await self.chat_engine.generator(
            system_prompt=self.system_prompt,
            prompt=rewrite_prompt,
            temperature=0)
        content = res.choices[0].message.content.strip()

        if "<think>" in content:
            content = re.sub(r"<think>(.*?)</think>", "", content, flags=re.DOTALL)
        return json_repair.loads(content.strip())


    @staticmethod
    def history_prompt(history: list[ChatMessage]):
        return "".join([f"user: {history.user}\nassistant: {history.assistant}\n" for history in history])


if __name__ == '__main__':
    import asyncio
    print(asyncio.run(RewriteEngine().rewrite(
        # history=[
        #     ChatHistory(user="M421有没有奥特曼和怪兽？", assistant="M421星球是布莱泽奥特曼诞生的星球，其文明比奥特之星更加原始，存在巨大怪兽，布莱泽一边打猎怪兽一边生活，还会用打倒的怪兽的铠甲或者骨头制造武器。"),
        #     ChatHistory(user="行星朱罗呢？",assistant="行星朱罗是高斯奥特曼的老家，这个星球上不仅有奥特曼，还有很多的怪兽，甚至有人类一样的生命体存在。"),
        #
        # ],
        # query="奥特曼喜欢吃pingguo吗？"
        # query="上班忘记打卡怎么办",
        # query="M78星云呢",
        query="中央扶手箱所支持的U盘及数据传输格式有哪些？"
    )))