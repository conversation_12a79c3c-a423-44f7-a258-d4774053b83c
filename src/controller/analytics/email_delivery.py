#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import Enum

from sqlalchemy import select, update

from model.analytics import AnalyticsEmailDeliveryModel
from engine.rdb import g, fetch_one


class ElementType(str, Enum):
    overview = "overview"
    charts = "charts"


class EmailDeliveryController:
    @staticmethod
    async def get_one(dashboard_id: int):
        query = (
            select(
                AnalyticsEmailDeliveryModel.address,
                AnalyticsEmailDeliveryModel.element)
            .where(AnalyticsEmailDeliveryModel.dashboard_id == dashboard_id))

        return await fetch_one(query)

    @staticmethod
    async def create(dashboard_id: int, address: list[str], element: list[str]):
        email_delivery = AnalyticsEmailDeliveryModel(dashboard_id=dashboard_id, address=address, element=element)
        g.session.add(email_delivery)

    @staticmethod
    async def update(dashboard_id: int, address: list[str] = None, element: list[str] = None):
        update_info = {}
        if address is not None:
            update_info[AnalyticsEmailDeliveryModel.address] = address
        if element is not None:
            update_info[AnalyticsEmailDeliveryModel.element] = element

        query = (
            update(AnalyticsEmailDeliveryModel)
            .where(AnalyticsEmailDeliveryModel.dashboard_id == dashboard_id)
            .values(update_info))

        await g.session.execute(query)


EmailDelivery = EmailDeliveryController()
