#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import select, update

from model.analytics import AnalyticsChartModel
from engine.rdb import g, query_order, fetch_all


class ChartsController:
    @staticmethod
    def get_query(dashboard_id: int = None, order_by: str = None):
        where = [AnalyticsChartModel.is_delete == 0]
        if dashboard_id is not None:
            where.append(AnalyticsChartModel.dashboard_id == dashboard_id)

        query = (
            select(
                AnalyticsChartModel.id.label("chart_id"),
                AnalyticsChartModel.config)
            .where(*where)
        )
        query = query_order(query=query, table=AnalyticsChartModel, order_by=order_by)

        return query

    async def get_all(self, dashboard_id: int = None, order_by: str = None):
        query = self.get_query(dashboard_id=dashboard_id, order_by=order_by)
        return await fetch_all(query=query)

    @staticmethod
    async def create(dashboard_id: int, config: dict):
        chart = AnalyticsChartModel(dashboard_id=dashboard_id, config=config)
        g.session.add(chart)
        await g.session.flush()

        return chart.id

    @staticmethod
    async def update(chart_id: int, config: dict = None):
        update_info = {}
        if config is not None:
            update_info[AnalyticsChartModel.config] = config

        query = (update(AnalyticsChartModel)
                 .where(AnalyticsChartModel.id == chart_id)
                 .values(update_info))
        await g.session.execute(query)

    @staticmethod
    async def delete(chart_id: int):
        query = (update(AnalyticsChartModel)
                 .where(AnalyticsChartModel.id == chart_id)
                 .values({AnalyticsChartModel.is_delete: 1}))
        await g.session.execute(query)


Charts = ChartsController()
