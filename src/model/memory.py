#!/usr/bin/env python3
# -*- coding: utf-8 -*-
MEMORY_INDEX = "memory"

MEMORY_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "index.mapping.nested_objects.limit": 10000,
        "similarity": {
            "custom_bm25": {
                "type": "BM25",
                "k1": 0.5,  # 词频饱和度 默认值1.2
                "b": 0.2  # 长度归一化强度 默认0.75
            }
        },
        "analysis": {
            "filter": {
                # 停用词过滤器
                "stop_words_filter": {
                    "type": "stop",
                    "ignore_case": True,
                    "stopwords_path": "/usr/share/elasticsearch/config/stopwords.txt"
                },
                # 文本数字变化切分
                # 主要用来处理连续数字
                "word_delimiter_graph_filter": {
                    "type": "word_delimiter_graph",
                    "adjust_offsets": False  # 禁止修改偏移量
                },
                # 字符小写转换过滤器
                "lowercase_filter": {
                    "type": "lowercase"
                },
            },
            "analyzer": {
                "ik_smart": {
                    "type": "custom",
                    "tokenizer": "ik_smart",
                    "filter": [
                        "word_delimiter_graph_filter",  # 数字转换器
                        "lowercase_filter",  # 大小写转换器
                        "stop_words_filter",  # 停用词分词器
                    ]
                },
                "ik_max_word": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": [
                        "lowercase_filter",
                        "word_delimiter_graph_filter",
                    ]
                },
            },
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 内存ID
            "memory_id": {
                "type": "keyword"
            },
            # 用户ID
            "user_id": {
                "type": "keyword"
            },
            # 智能体ID(模仿mem0)
            "agent_id": {
                "type": "keyword"
            },
            # 执行ID(模仿mem0)
            "run_id": {
                "type": "keyword"
            },
            # 记忆内容
            "memory": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart",
                "similarity": "custom_bm25"
            },
            # 文本向量
            "vector": {
                "type": "dense_vector",
                "dims": 1024
            },
            # 创建时间
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            },
            # 更新时间
            "update_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            }
        }
    }
}
