#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class UserPreferConfigModel(BaseModel):
    __tablename__ = "user_prefer_config"
    __comment__ = "用户输出偏好配置表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="配置ID")
    config: Mapped[list] = mapped_column(nullable=False, default='[]', comment="配置内容")


class UserRetrieveConfigModel(BaseModel):
    __tablename__ = "user_retrieve_config"
    __comment__ = "用户检索配置表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="配置ID")
    config: Mapped[list] = mapped_column(nullable=False, default='[]', comment="配置内容")