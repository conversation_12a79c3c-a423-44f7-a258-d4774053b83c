#!/usr/bin/env python3
# -*- coding: utf-8 -*-
METRICS_INDEX = "metrics"

METRICS_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "index.mapping.nested_objects.limit": 10000,
        "analysis": {
            "filter": {
                # 停用词过滤器
                "stop_words_filter": {
                    "type": "stop",
                    "ignore_case": True,
                    "stopwords_path": "/usr/share/elasticsearch/config/stopwords.txt"
                },
                # 文本数字变化切分
                # 主要用来处理连续数字
                "word_delimiter_graph_filter": {
                    "type": "word_delimiter_graph",
                    "adjust_offsets": False  # 禁止修改偏移量
                },
                # 字符小写转换过滤器
                "lowercase_filter": {
                    "type": "lowercase"
                },
            },
            "analyzer": {
                "ik_smart": {
                    "type": "custom",
                    "tokenizer": "ik_smart",
                    "filter": [
                        "word_delimiter_graph_filter",  # 数字转换器
                        "lowercase_filter",  # 大小写转换器
                        "stop_words_filter",  # 停用词分词器
                    ]
                },
                "ik_max_word": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": [
                        "lowercase_filter",
                        "word_delimiter_graph_filter",
                    ]
                },
            },
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 分析面板ID
            "dashboard_id": {
                "type": "integer"
            },
            # 租户ID
            "tenant_id": {
                "type": "integer"
            },
            # 数据项ID
            "item_id": {
                "type": "integer"
            },
            # 任务ID
            "task_id": {
                "type": "integer"
            },
            # 任务状态
            # 枚举见AnalyticsTaskStatus
            "status": {
                "type": "keyword"
            },
            # 原始内容
            "source_content": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            },
            # 提取指标
            "extract_metrics": {
                "type": "object",
                "properties": {
                    "metric_id": {
                        "type": "integer"
                    },
                    "value": {
                        "type": "keyword",
                        "fields": {
                            "text": {
                                "type": "text",
                                "analyzer": "ik_max_word",
                                "search_analyzer": "ik_smart"
                            }
                        }
                    },
                    "explanation": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    }
                }
            },
            "copy_to": {
                "type": "keyword"
            },
            "summarizer_task_ids": {
                "type": "keyword"
            },
            "input_tokens": {
                "type": "integer",
            },
            "output_tokens": {
                "type": "integer",
            },
            # 数据时间
            "data_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            },
            # 创建时间
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis"
            }
        }
    }
}
