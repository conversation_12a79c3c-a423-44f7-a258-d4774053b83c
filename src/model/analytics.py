#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel, g_attr


class AnalyticsDashboardModel(BaseModel):
    __tablename__ = "analytics_dashboard"
    __comment__ = "分析面板表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="分析面板ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")
    name: Mapped[str] = mapped_column(String(12), nullable=False, comment="分析面板名称")
    execution: Mapped[str] = mapped_column(String(16), nullable=False, comment="执行时间")
    combin_summarizer_ids: Mapped[list] = mapped_column(nullable=False, comment="组合摘要分析IDs")
    sup_summarizer_ids: Mapped[list] = mapped_column(nullable=False, comment="补充摘要分析IDs")
    focus: Mapped[str] = mapped_column(String(50), nullable=False, comment="分析方向")
    add_info: Mapped[str] = mapped_column(String(200), nullable=True, comment="补充信息")


class AnalyticsItemModel(BaseModel):
    __tablename__ = "analytics_item"
    __comment__ = "分析面板数据项表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="数据项ID")
    dashboard_id: Mapped[int] = mapped_column(nullable=False, comment="分析面板ID")
    name: Mapped[str] = mapped_column(String(256), nullable=False, comment="数据项名称")
    combin_summarizer_ids: Mapped[list] = mapped_column(nullable=False, default="[]", comment="组合摘要分析IDs")
    sup_summarizer_ids: Mapped[list] = mapped_column(nullable=False, default="[]", comment="补充摘要分析IDs")


class AnalyticsTaskModel(BaseModel):
    __tablename__ = "analytics_task"
    __comment__ = "分析面板异步任务"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="任务ID")
    dashboard_id: Mapped[int] = mapped_column(nullable=False, comment="分析面板ID")
    data_time: Mapped[datetime] = mapped_column(nullable=False, comment="数据时间")
    status: Mapped[int] = mapped_column(nullable=False, comment="任务状态")


class AnalyticsMetricModel(BaseModel):
    __tablename__ = "analytics_metric"
    __comment__ = "分析面板关联量化指标表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="分析面板关联量化指标ID")
    dashboard_id: Mapped[int] = mapped_column(nullable=False, comment="分析面板ID")
    name: Mapped[str] = mapped_column(String(12), nullable=False, comment="量化指标名称")
    enum_: Mapped[list] = mapped_column(nullable=True, comment="枚举类型选项")
    type_: Mapped[str] = mapped_column(String(12), nullable=False, comment="量化指标类型")
    min_: Mapped[int] = mapped_column(nullable=False, comment="数值最小值")
    max_: Mapped[int] = mapped_column(nullable=False, comment="数值最大值")
    require_: Mapped[str] = mapped_column(String(500), nullable=False, comment="量化指标分析要求")


class AnalyticsChartModel(BaseModel):
    __tablename__ = "analytics_chart"
    __comment__ = "图表表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="分析面板图表ID")
    dashboard_id: Mapped[int] = mapped_column(nullable=False, comment="分析面板ID")
    config: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="图表配置")


class AnalyticsEmailDeliveryModel(BaseModel):
    __tablename__ = "analytics_email_delivery"
    __comment__ = "定时邮件推送表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="分析面板邮件推送ID")
    dashboard_id: Mapped[int] = mapped_column(nullable=False, comment="分析面板ID")
    address: Mapped[list] = mapped_column(nullable=False, comment="邮件地址")
    element: Mapped[list] = mapped_column(nullable=False, comment="内容要素")
