#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from common import g
from model.base import BaseModel


class TokenCountsModel(BaseModel):
    __tablename__ = "token_counts"
    __comment__ = "Token用量计数表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="Token计数ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g.tenant_id, comment="租户ID")
    model_name: Mapped[str] = mapped_column(String(128), nullable=False, comment="应用模型")
    business: Mapped[str] = mapped_column(String(32), nullable=False, comment="应用业务")
    input_tokens: Mapped[int] = mapped_column(nullable=False, comment="输入Token数量")
    output_tokens: Mapped[int] = mapped_column(nullable=False, comment="输出Token数量")
