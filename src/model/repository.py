#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel
from common import g


class RepositoryType(StrEnum):
    public = "public"
    private = "private"


class RepositoryModel(BaseModel):
    __tablename__ = "repository"
    __comment__ = "知识库表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="知识库ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g.tenant_id, comment="租户ID")
    name: Mapped[str] = mapped_column(String(50), nullable=False, comment="知识库名称")
    type_: Mapped[str] = mapped_column(String(16), nullable=False, comment="知识库类型")
    extract_model_id: Mapped[int] = mapped_column(nullable=True, comment="AI抽取模型ID")
