#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum

from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class ModelType(StrEnum):
    llm = "llm"
    embedding = "embedding"
    rerank = "rerank"


class ModelInfoModel(BaseModel):
    __tablename__ = "model_info"
    __comment__ = "模型信息表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="模型ID")
    show_name: Mapped[str] = mapped_column(String(64), nullable=False, comment="模型展示名称")
    model_name: Mapped[str] = mapped_column(String(64), nullable=False, comment="模型调用名称")
    icon: Mapped[str] = mapped_column(String(32), nullable=True, comment="模型图标")
    describe: Mapped[str] = mapped_column(String(256), nullable=False, comment="模型描述")
    url: Mapped[str] = mapped_column(String(512), nullable=False, comment="模型地址")
    apikey: Mapped[str] = mapped_column(String(1024), nullable=True, comment="模型API Key")
    model_type: Mapped[str] = mapped_column(String(16), nullable=False, comment="模型类型")
    max_input_tokens: Mapped[int] = mapped_column(nullable=False, comment="最大输入tokens")
    batch_size: Mapped[int] = mapped_column(nullable=False, comment="单批次任务数")
    function_calling: Mapped[bool] = mapped_column(nullable=False, comment="是否支持function calling")
    json_output: Mapped[bool] = mapped_column(nullable=False, comment="是否支持json输出")
    structured_output: Mapped[bool] = mapped_column(nullable=False, comment="是否支持结构化输出")
    concurrency: Mapped[int] = mapped_column(nullable=False, comment="并发数")
    extra: Mapped[dict] = mapped_column(nullable=False, default='{}', comment="其他配置")
    chat: Mapped[bool] = mapped_column(nullable=False, comment="是否支持chat")
    thinking: Mapped[bool] = mapped_column(nullable=False, comment="是否支持thinking")
    deep_research: Mapped[bool] = mapped_column(nullable=False, comment="是否支持deep research")
    extract: Mapped[bool] = mapped_column(nullable=False, comment="是否支持extract")
    abstract: Mapped[bool] = mapped_column(nullable=False, comment="是否支持abstract")
    analyze: Mapped[bool] = mapped_column(nullable=False, comment="是否支持analyze")
