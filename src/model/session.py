import datetime
import uuid
from enum import Enum, StrEnum
from typing import List, Annotated, Optional

from pydantic import BaseModel, Field, model_validator
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects import mysql
from sqlalchemy import String, Text

from model.base import BaseModel as OrmBaseModel, g_attr
from common.time import now_tz_datestring_with_millis, now_tz_datestring
from common import g
from controller.operator.chunking import RetrieveChunkModel


class ChatSessionType(StrEnum):
    CHAT = "chat"
    QA_DOC = "qa_doc"
    QA_ONE_DOC = "qa_one_doc"
    IMAGE = "image"
    DEEP_RESEARCH = "deep_research"


class QAStatus(StrEnum):
    success = "success"
    timeout = "timeout"
    no_reference = "no_reference"
    sensitive = "sensitive"
    error = "error"
    regenerate = "regenerate"
    stop = "stop"
    llm_response_error = "llm_response_error"


class DeepResearchStatus(StrEnum):
    pending = "pending"
    planning = "planning"
    plan_rejected = "plan_rejected"
    planed = "planed"
    researching = "researching"
    success = "success"
    error = "error"
    stop = "stop"


class ChatMessage(BaseModel):
    # 核心字段
    request_id: Annotated[str, Field(title="请求ID", default_factory=lambda: str(uuid.uuid4()))]
    user: Annotated[str, Field(title="用户输入内容")]  # 构建阶段写入
    qa_status: Annotated[QAStatus, Field(title="最终状态")] = QAStatus.success  # generator阶段结束时修改
    rewrite_user: Annotated[Optional[str], Field(title="用户输入内容改写结果")] = None  # retriever阶段写入
    thinking: Annotated[Optional[str], Field(title="模型思考内容")] = None  # generator阶段写入
    assistant: Annotated[Optional[str], Field(title="模型回复内容")] = None  # generator阶段写入
    is_delete: Annotated[bool, Field(title="是否软删除")] = False  # 默认值初始化,前端调用删除接口时修改
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None  # generator阶段捕获错误时写入
    reference: Annotated[Optional[List[dict | RetrieveChunkModel]], Field(title="参考资料")] = None  # generator阶段写入
    repo_ids: Annotated[Optional[List[int]], Field(title="知识库IDs")] = []  # 构建阶段写入
    doc_ids: Annotated[Optional[List[int]], Field(title="文档IDs")] = []  # 构建阶段写入
    web_search: Annotated[bool, Field(title="是否使用网络搜索")] = False  # 构建阶段写入
    config: Annotated[Optional[dict], Field(title="各种请求配置")] = None  # 多阶段写入
    user_id: Annotated[int, Field(title="用户ID")] = g.user_id  # 默认值初始化
    query_time: Annotated[str | datetime.datetime, Field(title="提问时间")] = now_tz_datestring_with_millis()  # 构建阶段写入

    # 统计/边缘功能
    chat_model_id: Annotated[Optional[int], Field(title="模型展示名称")] = None  # indexing阶段写入
    filter_docs: Annotated[Optional[List[dict]], Field(title="知识库文档定位")] = []  # retrieving阶段写入
    search_repo_chunks: Annotated[Optional[List[dict]], Field(title="检索到的文档片段")] = []  # retrieving阶段写入
    web_search_docs: Annotated[Optional[List[dict]], Field(title="网络搜索URL")] = []  # retrieving阶段写入
    search_start_time: Annotated[Optional[str | datetime.datetime], Field(title="搜索开始时间")] = None  # retrieving阶段写入
    repo_search_end_time: Annotated[Optional[str | datetime.datetime], Field(title="知识库搜索结束时间")] = None  # retrieving阶段写入
    web_extract_start_time: Annotated[Optional[str | datetime.datetime], Field(title="网络抽取结束时间")] = None  # retrieving阶段写入
    web_search_end_time: Annotated[Optional[str | datetime.datetime], Field(title="网络搜索结束时间")] = None  # retrieving阶段写入
    search_end_time: Annotated[Optional[str | datetime.datetime], Field(title="搜索结束时间")] = None  # retrieving阶段写入
    rerank_end_time: Annotated[Optional[str | datetime.datetime], Field(title="重排序结束时间")] = None  # retrieving阶段写入
    streaming_start_time: Annotated[Optional[str | datetime.datetime], Field(title="流式回答开始时间")] = None  # generator阶段写入
    streaming_end_time: Annotated[Optional[str | datetime.datetime], Field(title="流式回答结束时间")] = None  # generator阶段写入


class DeepResearchMessage(BaseModel):
    session_id: Annotated[int, Field(title="会话ID")]
    status: Annotated[DeepResearchStatus, Field(title="当前状态")] = DeepResearchStatus.pending
    user: Annotated[Optional[str], Field(title="用户输入内容")] = None
    model_plan: Annotated[Optional[str], Field(title="模型生成的研究计划")] = None
    user_plan: Annotated[Optional[str], Field(title="用户修改后的研究计划")] = None
    model_configs: Annotated[Optional[dict], Field(title="模型配置")] = None
    create_time: Annotated[str | datetime.datetime, Field(title="创建时间")] = now_tz_datestring_with_millis()
    plan_start_time: Annotated[Optional[str | datetime.datetime], Field(title="计划开始时间")] = None
    plan_end_time: Annotated[Optional[str | datetime.datetime], Field(title="计划结束时间")] = None
    research_start_time: Annotated[Optional[str | datetime.datetime], Field(title="研究开始时间")] = None
    research_end_time: Annotated[Optional[str | datetime.datetime], Field(title="研究结束时间")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class ChatSession(BaseModel):
    session_id: Annotated[int, Field(title="会话ID")]
    session_type: Annotated[ChatSessionType, Field(title="会话类型")]
    session_title: Annotated[Optional[str], Field(title="会话标题")] = None
    tenant_id: Annotated[int, Field(title="租户ID")] = None
    user_id: Annotated[int, Field(title="用户ID")] = None
    create_time: Annotated[Optional[str], Field(title="创建时间")] = None
    is_delete: Annotated[bool, Field(title="是否软删除")] = False
    chat_history: Annotated[List[ChatMessage], Field(title="聊天记录")] = []

    class Config:
        use_enum_values = True

    @model_validator(mode="after")
    def complete_fields(self):
        if self.create_time is None:
            self.create_time = now_tz_datestring()
        if self.user_id is None:
            self.user_id = g.user_id
        if self.tenant_id is None:
            self.tenant_id = g.tenant_id
        return self


class ChatSessionModel(OrmBaseModel):
    __tablename__ = "chat_session"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="会话ID")
    session_type: Mapped[str] = mapped_column(String(32), nullable=False, comment="会话类型")
    session_title: Mapped[str] = mapped_column(String(32), nullable=True, comment="会话标题")
    tenant_id: Mapped[int] = mapped_column(nullable=False, default=g_attr("tenant_id"), comment="租户ID")


class ChatMessageModel(OrmBaseModel):
    __tablename__ = "chat_message"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="会话ID")
    request_id: Mapped[str] = mapped_column(String(64), index=True, nullable=False, comment="请求ID")
    session_id: Mapped[int] = mapped_column(index=True, nullable=False, comment="会话ID")
    user: Mapped[str] = mapped_column(String(256), nullable=False, comment="用户输入内容")
    thinking: Mapped[str] = mapped_column(
        Text().with_variant(mysql.MEDIUMTEXT(), "mysql"), nullable=True, comment="模型思考内容")
    assistant: Mapped[str] = mapped_column(
        Text().with_variant(mysql.MEDIUMTEXT(), "mysql"), nullable=True, comment="模型回复内容")
    rewrite_user: Mapped[str] = mapped_column(String(512), nullable=True, comment="用户输入内容改写结果")
    chat_model_id: Mapped[int] = mapped_column(nullable=True, comment="语言模型ID")
    repo_ids: Mapped[list] = mapped_column(nullable=True, comment="知识库IDs")
    doc_ids: Mapped[list] = mapped_column(nullable=True, comment="文档IDs")
    query_time: Mapped[datetime.datetime] = mapped_column(nullable=False, comment="提问时间")
    info: Mapped[dict] = mapped_column(nullable=False, default="{}", comment="其他重要信息")
    status: Mapped[str] = mapped_column(String(32), nullable=False, comment="最终状态")
    search_start_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="多任务搜索开始时间")
    search_end_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="多任务搜索结束时间")
    rerank_end_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="重排序结束时间")
    streaming_start_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="流式回答开始时间")
    streaming_end_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="流式回答结束时间")
    error_msg: Mapped[str] = mapped_column(String(512), nullable=True)


class DeepResearchMessageModel(OrmBaseModel):
    __tablename__ = "deep_research_message"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="深度研究消息ID")
    session_id: Mapped[int] = mapped_column(index=True, nullable=False, comment="会话ID")
    status: Mapped[str] = mapped_column(String(32), nullable=False, comment="当前状态")
    user: Mapped[str] = mapped_column(String(512), nullable=True, comment="用户输入内容")
    model_plan: Mapped[str] = mapped_column(
        Text().with_variant(mysql.MEDIUMTEXT(), "mysql"), nullable=True, comment="模型生成的研究计划")
    user_plan: Mapped[str] = mapped_column(
        Text().with_variant(mysql.MEDIUMTEXT(), "mysql"), nullable=True, comment="用户修改后的研究计划")
    model_configs: Mapped[dict] = mapped_column(
        nullable=True, default="{}", comment="模型配置")
    create_time: Mapped[datetime.datetime] = mapped_column(nullable=False, comment="创建时间")
    plan_start_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="计划开始时间")
    plan_end_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="计划结束时间")
    research_start_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="研究开始时间")
    research_end_time: Mapped[datetime.datetime] = mapped_column(nullable=True, comment="研究结束时间")
    error_msg: Mapped[str] = mapped_column(
        Text().with_variant(mysql.MEDIUMTEXT(), "mysql"), nullable=True)
