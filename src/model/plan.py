#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from enum import StrEnum
from datetime import datetime


from sqlalchemy import String
from sqlalchemy.orm import Mapped, mapped_column

from model.base import BaseModel


class RefreshType(StrEnum):
    one_time = "one_time"
    period = "period"


class PlanType(StrEnum):
    tenant = "tenant"
    user = "user"


class PlanModel(BaseModel):
    __tablename__ = "plan"
    __comment__ = "订购套餐表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="套餐ID")
    plan_type: Mapped[str] = mapped_column(String(16), nullable=False, comment="套餐类型")
    name: Mapped[str] = mapped_column(String(64), nullable=False, comment="套餐名称")
    tokens: Mapped[int] = mapped_column(nullable=False, comment="套餐Token数量")
    refresh_type: Mapped[str] = mapped_column(String(16), nullable=False, comment="套餐刷新类型")
    refresh_period_days: Mapped[int] = mapped_column(nullable=False, default=0, comment="套餐刷新周期")
    refresh_count: Mapped[int] = mapped_column(nullable=False, default=0,  comment="套餐刷新次数")


class TenantPlanModel(BaseModel):
    __tablename__ = "tenant_plan"
    __comment__ = "租户订购套餐关系表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="关系ID")
    tenant_id: Mapped[int] = mapped_column(nullable=False, comment="租户ID")
    plan_id: Mapped[int] = mapped_column(nullable=False, comment="套餐ID")
    token_limit: Mapped[int] = mapped_column(nullable=False, comment="套餐Token限制")
    token_used: Mapped[int] = mapped_column(nullable=False, default=0, comment="套餐Token使用量")
    expire_time: Mapped[datetime] = mapped_column(nullable=True, comment="套餐过期时间")
    refresh_time: Mapped[datetime] = mapped_column(nullable=True, comment="下次刷新时间")
    remain_refresh_count: Mapped[int] = mapped_column(nullable=False, default=0, comment="剩余刷新次数")


class UserPlanModel(BaseModel):
    __tablename__ = "user_plan"
    __comment__ = "用户订购套餐关系表"

    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True, nullable=False, comment="关系ID")
    user_id: Mapped[int] = mapped_column(nullable=False, comment="用户ID")
    plan_id: Mapped[int] = mapped_column(nullable=False, comment="套餐ID")
    token_limit: Mapped[int] = mapped_column(nullable=False, comment="套餐Token限制")
    token_used: Mapped[int] = mapped_column(nullable=False, default=0, comment="套餐Token使用量")
    expire_time: Mapped[datetime] = mapped_column(nullable=True, comment="套餐过期时间")
    refresh_time: Mapped[datetime] = mapped_column(nullable=True, comment="下次刷新时间")
    refresh_count: Mapped[int] = mapped_column(nullable=False, default=0, comment="剩余刷新次数")
