#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body
from pydantic import Field

from view import BaseView, api_description
from controller.stats import Stats
from exception import NotFoundError


class StatsCompareView(BaseView):
    @api_description(summary="统计分析")
    async def post(self,
                   left: Annotated[str, Body(), Field(title="左侧数据cache_id")],
                   right: Annotated[str, Body(), Field(title="右侧数据cache_id")] = None):
        left_data = await Stats.get_cache(name=left)
        if left_data is None:
            raise NotFoundError(message="数据已过期,请重新分析")

        if right is None:
            prompt = Stats.data_analyze_prompt(data=left_data)
        else:
            right_data = await Stats.get_cache(name=right)
            if right_data is None:
                raise NotFoundError(message="数据已过期,请重新分析")
            prompt = Stats.data_compare_prompt(left=left_data, right=right_data)

        return self.stream(Stats.compare_generator(prompt))
