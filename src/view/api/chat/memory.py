#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body

from controller.memory import Memory
from view import BaseView, api_description


class ChatMemoryView(BaseView):
    @api_description(summary="查询用户全部记忆")
    async def get(self):
        memories = await Memory.get_all(user_id=self.user_id)
        print(memories)
        return self.response(data=memories)