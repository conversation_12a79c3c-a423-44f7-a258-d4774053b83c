from typing import Annotated

from fastapi import Query, Body

from engine.rdb import g
from controller.chat.chat import Chat<PERSON>elper
from controller.chat.session import Session, ChatSessionType
from view import BaseView, api_description


class ChatSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[int, Query(title="Session ID")]):
        session_item = await Session.get_one(session_id=session_id)
        return self.response(data=session_item.model_dump())

    @api_description(summary="创建会话")
    async def post(self):
        session_id = await Session.create(ChatSessionType.CHAT)
        await g.session.commit()
        return self.response(data={"session_id": session_id})


class ChatView(BaseView):
    @api_description(summary="开始自由问答")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   session_id: Annotated[int, Body(title="会话ID")] = None):
        if session_id is None:
            session_item = await Session.create_es(ChatSessionType.CHAT)
            session_id = session_item.session_id

        chat_helper = ChatHelper(session_id=session_id, user=user)
        return self.stream(chat_helper.generator())
