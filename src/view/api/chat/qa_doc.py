from typing import Annotated, List

from fastapi import Query, Body

from config.models import LLMModel
from common import g
from view import BaseView, api_description
from controller.chat.doc_qa import DocQAHelper
from controller.chat.session import Session, ChatSessionType
from controller.retriever import WebSearchConfig


class QAChatView(BaseView):
    @api_description(summary="开始问答")
    async def post(self,
                   user: Annotated[str, Body(title="用户输入的问题")],
                   session_id: Annotated[int, Body(title="会话ID")] = None,
                   chat_model: Annotated[str, Body(title="模型名称")] = LLMModel.DEEPSEEK_REASONER.value,
                   repo_ids: Annotated[list[int] | bool, Body(title="知识库IDs")] = None,
                   doc_ids: Annotated[List[int], Body(title="文档ID")] = None,
                   user_retrieve: Annotated[bool, Body(title="是否使用用户检索配置")] = False,
                   web_search: Annotated[WebSearchConfig, Body(title="网络搜索配置")] = None):
        if session_id is None:
            if doc_ids and len(doc_ids) == 1:
                session_type = ChatSessionType.QA_ONE_DOC
            else:
                session_type = ChatSessionType.QA_DOC
            session_id = await Session.create(session_type)
            await g.session.commit()

        chat_helper = DocQAHelper(
            session_id=session_id,
            user=user,
            chat_model=chat_model,
            repo_ids=repo_ids,
            doc_ids=doc_ids,
            user_retrieve=user_retrieve,
            web_search=web_search
        )

        return self.stream(chat_helper.generator())


class QAChatStopView(BaseView):
    @api_description(summary="停止问答")
    async def post(self,
                   session_id: Annotated[int, Body(title="会话ID", embed=True)]):
        await Session.stop_streaming(session_id=session_id)
        return self.response(message="停止成功")
