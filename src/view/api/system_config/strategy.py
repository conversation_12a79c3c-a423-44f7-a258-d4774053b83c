#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Body
from pydantic import Field

from engine.rdb import g
from view import BaseView, api_description
from controller.system_config import Strategy, StrategyConfig, ChatRetrieveStrategy, ParserStrategy


class StrategyView(BaseView):
    @api_description(summary="获取策略配置")
    async def get(self):
        strategy = await Strategy.get()

        return self.response(data=strategy)

    @api_description(summary="更新策略配置")
    async def put(self,
                  parser: Annotated[ParserStrategy, Body(), Field(title="解析策略配置")],
                  chat_retrieve: Annotated[ChatRetrieveStrategy, Body(), Field(title="问答召回策略配置")]):
        await Strategy.create(strategy=StrategyConfig(parser=parser, chat_retrieve=chat_retrieve))
        await g.session.commit()

        await Strategy.set_cache(
            tenant_id=g.tenant_id,
            strategy=StrategyConfig(parser=parser, chat_retrieve=chat_retrieve))

        return self.response(message="更新成功")
