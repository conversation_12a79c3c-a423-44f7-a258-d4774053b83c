#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from pydantic import Field
from fastapi import Query, Body

from engine.rdb import g
from view import BaseView, api_description
from controller.system_config import ModelInfo, ModelType


class ModelConfigView(BaseView):
    @api_description(summary="获取模型配置")
    async def get(self,
                  model_type: Annotated[ModelType, Query(), Field(title="模型类型")] = None):
        models = await ModelInfo.get_all(model_type=model_type)
        return self.response(data=models)

    @api_description("修改模型配置")
    async def put(self,
                  model_id: Annotated[int, Body(), Field(title="模型ID")],
                  show_name: Annotated[str, Body(), Field(title="模型展示名称")] = None,
                  model_name: Annotated[str, Body(), Field(title="模型名称")] = None,
                  describe: Annotated[str, Body(), Field(title="模型描述")] = None,
                  url: Annotated[str, Body(), Field(title="模型地址")] = None,
                  apikey: Annotated[str, Body(), Field(title="模型API Key")] = None,
                  model_type: Annotated[ModelType, Body(), Field(title="模型类型")] = None,
                  max_input_tokens: Annotated[int, Body(), Field(title="最大输入tokens", ge=1)] = None,
                  batch_size: Annotated[int, Body(), Field(title="批处理数量", ge=1)] = None,
                  function_calling: Annotated[bool, Body(), Field(title="是否支持函数调用")] = None,
                  json_output: Annotated[bool, Body(), Field(title="是否支持JSON输出")] = None,
                  structured_output: Annotated[bool, Body(), Field(title="是否支持结构化输出")] = None,
                  concurrency: Annotated[int, Body(), Field(title="并发数", ge=1)] = None,
                  extra: Annotated[dict, Body(), Field(title="额外信息")] = None,
                  chat: Annotated[bool, Body(), Field(title="是否支持chat")] = None,
                  thinking: Annotated[bool, Body(), Field(title="是否支持thinking")] = None,
                  deep_research: Annotated[bool, Body(), Field(title="是否支持deep research")] = None,
                  extract: Annotated[bool, Body(), Field(title="是否支持extract")] = None,
                  abstract: Annotated[bool, Body(), Field(title="是否支持abstract")] = None,
                  analyze: Annotated[bool, Body(), Field(title="是否支持analyze")] = None):
        await ModelInfo.update(
            model_id=model_id, show_name=show_name, model_name=model_name, describe=describe,url=url, apikey=apikey,
            model_type=model_type, max_input_tokens=max_input_tokens,  batch_size=batch_size,
            function_calling=function_calling, json_output=json_output, structured_output=structured_output,
            concurrency=concurrency, extra=extra, chat=chat, thinking=thinking, deep_research=deep_research,
            extract=extract, abstract=abstract, analyze=analyze)
        await g.session.commit()

        return self.response(message="更新成功")
