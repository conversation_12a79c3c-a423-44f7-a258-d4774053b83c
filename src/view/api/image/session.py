from typing import Annotated

from fastapi import Query

from controller.chat.session import Session, ChatSessionType
from view import BaseView, api_description


class ImageSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[int, Query(title="Session ID")]):
        session_item = await Session.get_es_one(session_id=session_id)
        return self.response(data=session_item.model_dump())

    @api_description(summary="创建会话")
    async def post(self):
        session_item = await Session.create_es(ChatSessionType.IMAGE)
        return self.response(data=session_item.model_dump())
