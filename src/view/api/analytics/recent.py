#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import Dashboard
from common import g


class DashboardRecentListView(BaseView):
    @api_description(summary="查询最近访问分析面板")
    async def get(self,
                  size: Annotated[int, Query(), Field(title="最近访问数量")] = 4):
        dashboard_recent = await Dashboard.get_recent(user_id=g.user_id, size=size)
        dashboards = await Dashboard.get_all(dashboard_ids=dashboard_recent)
        dashboards = list(sorted(dashboards, key=lambda db: dashboard_recent.index(db["dashboard_id"])))

        return self.response(data=dashboards)


class DashboardRecentView(BaseView):
    @api_description(summary="记录分析面板访问")
    async def post(self,
                   dashboard_id: Annotated[int, Body(embed=True), Field(title="分析面板ID")]):
        await Dashboard.set_recent(dashboard_id=dashboard_id, user_id=g.user_id)

        return self.response(message="记录成功")