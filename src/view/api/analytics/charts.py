#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from pydantic import Field

from typing import Annotated, Dict, Any
from fastapi import Query, Body 

from engine.rdb import g
from view import BaseView, api_description
from controller.analytics import Charts


class DashboardChartView(BaseView):
    @api_description(summary="查询全部图表配置")
    async def get(self,
                  dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")],
                  order_by: Annotated[str, Query(), Field(title="排序条件")] = "create_time:desc"):
        charts = await Charts.get_all(dashboard_id=dashboard_id, order_by=order_by)

        return self.response(data=charts)

    @api_description(summary="创建图表配置")
    async def post(self,
                   dashboard_id: Annotated[int, Body(), Field(title="分析面板ID")],
                   config: Annotated[Dict[str, Any], Body(title="图表配置")]):
        chart_id = await Charts.create(dashboard_id=dashboard_id, config=config)

        await g.session.commit()

        return self.response(data={"chart_id": chart_id})

    @api_description(summary="修改图表配置")
    async def put(self,
                  chart_id: Annotated[int, Body(), Field(title="图表ID")],
                  config: Annotated[Dict[str, Any], Body(title="图表配置")]):
        await Charts.update(chart_id=chart_id, config=config)

        await g.session.commit()

        return self.response(message="修改成功")

    @api_description(summary="删除图表配置")
    async def delete(self,
                     chart_id: Annotated[int, Body(embed=True), Field(title="图表ID")]):
        await Charts.delete(chart_id=chart_id)

        await g.session.commit()

        return self.response(message="删除成功")
