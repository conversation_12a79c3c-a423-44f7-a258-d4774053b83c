#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import Metrics


class AnalyticsTaskView(BaseView):
    @api_description(summary="分析任务列表")
    async def get(self,
                  dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")],
                  depth: Annotated[int, Query(), Field(title="数据时间深度")],
                  order_by: Annotated[str, Query(), Field(title="排序条件")] = "id:desc"):
        tasks = await Metrics.get_task_all(dashboard_id=dashboard_id, depth=depth, order_by=order_by)

        return self.response(data=tasks)
