#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import EmailDelivery, ElementType
from common import g


class DashboardEmailDeliveryView(BaseView):
    @api_description(summary="查询分析推送配置")
    async def get(self,
                  dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")]):
        email_config = await EmailDelivery.get_one(dashboard_id=dashboard_id)
        return self.response(data=email_config)

    @api_description(summary="更新分析推送配置")
    async def post(self,
                   dashboard_id: Annotated[int, Body(), Field(title="分析面板ID")],
                   address: Annotated[list[str], Body(), Field(title="推送地址")],
                   element: Annotated[list[ElementType], Body(), Field(title="推送内容")]):
        await EmailDelivery.update(dashboard_id=dashboard_id, address=address, element=element)
        await g.session.commit()
        return self.response(message="更新成功")
