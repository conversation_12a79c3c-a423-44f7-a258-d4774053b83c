#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from view import BaseView, api_description
from controller.repository import Repo
from common import g


class RepoRecentListView(BaseView):
    @api_description(summary="查询最近访问知识库")
    async def get(self,
                  size: Annotated[int, Query(), Field(title="最近访问数量")] = 4):
        repo_recent = await Repo.get_recent(user_id=g.user_id, size=size)
        repos = await Repo.get_all(repo_ids=repo_recent)
        repos = list(sorted(repos, key=lambda repo: repo_recent.index(repo["repo_id"])))

        return self.response(data=repos)


class RepoRecentView(BaseView):
    @api_description(summary="记录知识库访问")
    async def post(self,
                   repo_id: Annotated[int, Body(embed=True), Field(title="知识库ID")]):
        await Repo.set_recent(repo_id=repo_id, user_id=g.user_id)

        return self.response(message="记录成功")