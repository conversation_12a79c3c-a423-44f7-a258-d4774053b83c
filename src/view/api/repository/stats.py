#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.stats import Stats
from controller.stats.stats import StatsContent


class RepoStatsView(BaseView):
    @api_description(summary="知识库统计")
    async def get(self,
                  repo_id: Annotated[int, Query(), Field(title="知识库ID")],
                  start: Annotated[str, Query(), Field(title="开始时间")] = None,
                  end: Annotated[str, Query(), Field(title="结束时间")] = None):
        # ES文档统计
        stats_contents = [
            StatsContent.sentiment_score_trend,
            StatsContent.top_subject,
            StatsContent.top_doc_keywords
        ]
        stats_result = await Stats.get_doc_stats(
            stats_contents=stats_contents, repo_ids=[repo_id], start=start, end=end)

        return self.response(data=stats_result)
