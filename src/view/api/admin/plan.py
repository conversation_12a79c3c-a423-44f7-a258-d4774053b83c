#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query, Body
from pydantic import Field

from engine.rdb import g
from view import BaseView, api_description
from controller.admin import Plan, RefreshType
from exception import PermissionDenyError


class TenantPlanListView(BaseView):
    @api_description(summary="获取租户套餐列表")
    async def get(self,
                  page: Annotated[int, Query(), Field(title="页码")] = 1,
                  per_page: Annotated[int, Query(), Field(title="每页数量")] = 20,
                  order_by: Annotated[str, Query(), Field(title="排序字段")] = "create_time:desc"):
        if not self.is_super_admin:
            raise PermissionDenyError()
        pager, plans = await Plan.get_tenant_list(page=page, per_page=per_page, order_by=order_by)

        return self.response(data=plans, pager=pager)


class TenantPlanView(BaseView):
    @api_description(summary="创建租户套餐")
    async def post(self,
                   name: Annotated[str, Body(), Field(title="套餐名称")],
                   tokens: Annotated[int, Body(), Field(title="套餐Token数量")],
                   refresh_type: Annotated[RefreshType, Body(), Field(title="刷新类型")],
                   refresh_period_days: Annotated[int, Body(), Field(title="套餐刷新周期")] = 0,
                   refresh_count: Annotated[int, Body(), Field(title="套餐刷新次数", ge=0)] = 0):
        if not self.is_super_admin:
            raise PermissionDenyError()
        plan_id = await Plan.create_tenant(
            name=name, tokens=tokens, refresh_type=refresh_type, refresh_period_days=refresh_period_days,
            refresh_count=refresh_count)
        await g.session.commit()

        return self.response(data={"plan_id": plan_id})

    @api_description(summary="编辑租户套餐")
    async def put(self,
                  plan_id: Annotated[int, Body(), Field(title="套餐ID")],
                  name: Annotated[str, Body(), Field(title="套餐名称")] = None,
                  tokens: Annotated[int, Body(), Field(title="套餐Token数量")] = None,
                  refresh_type: Annotated[RefreshType, Body(), Field(title="刷新类型")] = None,
                  refresh_period_days: Annotated[int, Body(), Field(title="套餐刷新周期")] = None,
                  refresh_count: Annotated[int, Body(), Field(title="套餐刷新次数", ge=0)] = None):
        if not self.is_super_admin:
            raise PermissionDenyError()
        await Plan.update_tenant(
            plan_id=plan_id, name=name, tokens=tokens, refresh_type=refresh_type,
            refresh_period_days=refresh_period_days, refresh_count=refresh_count)
        await g.session.commit()

        return self.response(message="修改成功")

    @api_description(summary="删除租户套餐")
    async def delete(self,
                     plan_id: Annotated[int, Body(embed=True), Field(title="套餐ID")]):
        if not self.is_super_admin:
            raise PermissionDenyError()
        await Plan.delete_tenant(plan_id=plan_id)
        await g.session.commit()

        return self.response(message="删除成功")
