#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from lxml import etree, html as lxmlhtml



def _remove_style_attributes(element):
    """递归移除元素及其子元素的所有样式相关属性"""
    # 定义样式相关属性列表
    style_attrs = [
        "style", "class", "id", "align", "width", "height", "bgcolor", "color", "font", "margin", "padding",
        "border", "cellpadding", "cellspacing", "valign", "background", "face", "size", "text-align"
    ]

    # 从当前元素中移除样式属性
    for attr in style_attrs:
        if attr in element.attrib:
            del element.attrib[attr]

    # 递归处理所有子元素
    for child in element:
        _remove_style_attributes(child)

    return element

with open("1.html", mode="r", encoding="utf-8") as f:
    html_string = f.read()
tree = etree.fromstring(html_string, lxmlhtml.HTMLParser())
if tree is None:
    raise ValueError("html_content is not a valid html string")
root = tree.getroottree()  # 用于寻找xpath

body_tag = tree.xpath('//body')[0]
element = _remove_style_attributes(body_tag)
html_content = lxmlhtml.tostring(element, encoding="unicode", pretty_print=False)
print(html_content)