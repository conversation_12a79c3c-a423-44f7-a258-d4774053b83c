#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import time
from enum import StrEnum

from pathlib import Path

import httpx
from lxml import html as lxmlhtml
import fitz  # PyMuPDF
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import numpy as np


f = Path(r"C:\Users\<USER>\Downloads\2206.01062v1.pdf")

s = time.time()
parameters = {
    "return_middle_json": True,
    "return_model_output": False,
    "return_md": True,
    "return_images": True,
    "parse_method": "ocr",
    "backend": "pipeline",
    "table_enable": True,
    "return_content_list": False,
    "formula_enable": True,
    # "start_page_id": 19,
    # "end_page_id": 22
}
resp = httpx.post(
    url="http://bchampion.cn:11016/file_parse",
    data=parameters,
    files={
        "files": (f.name, open(f.as_posix(), mode="rb"))
    },
    timeout=300
)
resp_json = resp.json()
print(time.time() - s)
# print(resp_json)

file_result = resp_json["results"][f.stem]
md_content = file_result["md_content"]
images = file_result["images"]
paragraphs = md_content.split("\n")


class MinerUType(StrEnum):
    title = "title"
    text = "text"
    list = "list"
    image = "image"
    table = "table"
    formula = "formula"
    interline_equation = "interline_equation"


def storage_text(block: dict, full_line_radio: float = 0.7):
    text = ""
    block_bbox = block["bbox"]
    for i, line in enumerate(block["lines"]):
        spans = []
        for span in line["spans"]:
            if span.get("content"):
                spans.append(span["content"])
        text += " ".join(spans)

        if line.get("is_list_end_line"):
            text += " \n "  # 特殊标记
        if (
                text  # 已有文本
                and i != 0  # 索引不是第一位
                and text[-1] != " "
                and (
                        block["lines"][i-1]["bbox"][2] < block_bbox[2] * full_line_radio  # 上一行x1不足满行 full_line_radio%
                        or (
                                text[-1].isalpha() or ord(text[-1]) < 128  # 英文字母
                                or text[-1].isdigit()  # 或数字
                        )
                )):
            # 极大概率是换行, 增加换行符
            text += " "
    return text.strip()


def build_page_box(p: int, pages_size: dict, block: dict):
    # 2.1.1版本,para_blocks的bbox和其下lines.bbox坐标不对等.
    # 为了处理跨页问题,以lines.bbox为准
    boxes = []
    # cross_page标记只能管翻过一页,但是不管跨两页.
    # 使用标记强制翻第一页,根据bbox有选择的翻第二页
    cross_page_flag = False

    if block.get("blocks"):
        block.setdefault("lines", [])
        block["lines"].extend([line for b in block["blocks"] for line in b["lines"]])

    for line in block["lines"]:
        if not boxes:
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # layout判断跨超过一页的情况.需要cross_page不在本次出现,并且bbox有大幅变动
        if (cross_page_flag is True
                and abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30  # y0小于上个bbox y1 20个像素以上
                and line["bbox"][0] < boxes[-1]["bbox"][2]  # x0不大于上个bbox的x1
            ):
            p += 1
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 首次出现cross_page 强制翻页
        if line["spans"] and line["spans"][0].get("cross_page") and cross_page_flag is False:
            cross_page_flag = True
            p += 1
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 块产生较大变动,重新分块
        if line["bbox"][0] > boxes[-1]["bbox"][2] or abs(line["bbox"][1] - boxes[-1]["bbox"][3]) > 30:
            boxes.append({"page": p, "bbox": line["bbox"], "page_size": pages_size[p]})
            continue

        # 未检测到的其他情况,扩展bbox
        boxes[-1]["bbox"][0] = min(boxes[-1]["bbox"][0], line["bbox"][0])
        boxes[-1]["bbox"][1] = min(boxes[-1]["bbox"][1], line["bbox"][1])
        boxes[-1]["bbox"][2] = max(boxes[-1]["bbox"][2], line["bbox"][2])
        boxes[-1]["bbox"][3] = max(boxes[-1]["bbox"][3], line["bbox"][3])

    return boxes


doc_htmls = []
doc_plain_texts = []
middle_json = json.loads(file_result["middle_json"])
pages_size = {p["page_idx"] + 1: p["page_size"] for p in middle_json["pdf_info"]}
for page in middle_json["pdf_info"]:
    for block in page["para_blocks"]:
        type_ = block["type"]
        p = page["page_idx"] + 1
        bboxes = build_page_box(p=p, pages_size=pages_size, block=block)

        if type_ == MinerUType.image:
            plain_text = ""
            image_caption = []
            img_tags = []
            for b in block["blocks"]:
                # 图片本体,构造html标签
                if b["type"] == "image_body":
                    for line in b["lines"]:
                        for span in line["spans"]:
                            if span.get("image_path"):
                                img_tags.append(f'<img src="{images}">')
                # 图片描述
                if b["type"] == "image_caption":
                    text = storage_text(b)
                    plain_text += f"{text} "
                    image_caption.append(f"<span>{text}</span>")
            # 最后,如有多个image_caption,组合为figcaption
            html_content = (f"<figcaption>"
                            f"{''.join(img_tags)}"
                            f"{'<br>'.join(image_caption)}"
                            f"</figcaption>")

        elif type_ == MinerUType.table:
            plain_text = ""
            table_caption = []
            table_body = ""
            for b in block["blocks"]:
                # 表格本体,构造html标签
                if b["type"] == "table_body":
                    if table_body:
                        print("MinerU解析同para下出现多个表格")
                    for line in b["lines"]:
                        for span in line["spans"]:
                            table_body = span["html"][19:-22]
                            plain_text = lxmlhtml.fromstring(table_body).text_content()


                # 表格描述
                if b["type"] == "table_caption":
                    text = storage_text(b)
                    plain_text += f"{text} "
                    table_caption.append(text)

            html_content = "<table>" + "\n".join(table_caption) + table_body + "</table>"

        elif type_ == MinerUType.title:
            text = storage_text(block)
            plain_text = text
            if len(doc_htmls) < 2:
                html_content = f"<h1>{text}</h1>"
            else:
                html_content = f"<h2>{text}</h2>"

        elif type_ == MinerUType.list:
            text = storage_text(block)
            plain_text = text.replace(" \n ", " ")
            li_tags = [f"<li>{text}</li>" for text in text.split(" \n ")]
            html_content = f"<p>{''.join(li_tags)}</p>"

        elif type_ == MinerUType.text:
            text = storage_text(block)
            plain_text = text
            html_content = f"<p>{text}</p>"

        elif type_ == MinerUType.interline_equation:
            equations = []
            html_content = ""
            for line in block["lines"]:
                for span in line["spans"]:
                    equations.append(span["content"])
                    html_content += f'<p><img src="{images[span["image_path"]]}"></p>'
            plain_text = " \n ".join([f"$${eq}$$" for eq in equations])
        else:
            print(f"有未知的MinerU paragraph <{type_}>")

            text = storage_text(block)
            plain_text = f"$${text}$$"
            html_content = f'<p><img src="{images}"></p>'
            continue

        if not bboxes:
            continue
        doc_htmls.append({
            "html": html_content,
            "plain_text": plain_text,
            "bboxes": bboxes,
            "type": type_
        })


# PDF可视化功能：在PDF上画框显示bbox
def visualize_pdf_with_bboxes(pdf_path, doc_htmls, output_dir="output_images"):
    """
    在PDF上绘制bbox框并保存图像

    Args:
        pdf_path: PDF文件路径
        doc_htmls: 包含bbox信息的文档块列表
        output_dir: 输出图像目录
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    # 打开PDF文件
    try:
        pdf_doc = fitz.open(str(pdf_path))
    except Exception as e:
        print(f"无法打开PDF文件 {pdf_path}: {e}")
        return

    # 定义不同类型的颜色
    type_colors = {
        MinerUType.title: 'red',
        MinerUType.text: 'blue',
        MinerUType.list: 'green',
        MinerUType.image: 'purple',
        MinerUType.table: 'orange',
        MinerUType.formula: 'brown',
        MinerUType.interline_equation: 'pink'
    }

    # 按页面分组bbox
    page_bboxes = {}
    for block in doc_htmls:
        for bbox_info in block["bboxes"]:
            page_num = bbox_info["page"]
            if page_num not in page_bboxes:
                page_bboxes[page_num] = []
            page_bboxes[page_num].append({
                "bbox": bbox_info["bbox"],
                "page_size": bbox_info["page_size"],
                "type": block["type"],
                "text": block["plain_text"][:50] + "..." if len(block["plain_text"]) > 50 else block["plain_text"]
            })

    print(f"开始处理PDF，共 {len(pdf_doc)} 页")

    # 处理每一页
    for page_num in range(1, len(pdf_doc) + 1):
        if page_num not in page_bboxes:
            continue

        page = pdf_doc[page_num - 1]  # fitz页面索引从0开始

        # 将PDF页面转换为图像
        mat = fitz.Matrix(1.0, 1.0)  # 放大2倍以提高清晰度
        pix = page.get_pixmap(matrix=mat)
        img_data = pix.tobytes("ppm")

        # 使用matplotlib显示和标注
        fig, ax = plt.subplots(1, 1, figsize=(12, 16))

        # 将图像数据转换为numpy数组
        from PIL import Image
        import io
        img = Image.open(io.BytesIO(img_data))
        img_array = np.array(img)

        ax.imshow(img_array)
        ax.set_title(f'PDF页面 {page_num} - Bbox可视化', fontsize=14, fontweight='bold')

        # 获取页面尺寸用于坐标转换
        page_rect = page.rect
        img_height, img_width = img_array.shape[:2]

        # 绘制每个bbox
        for i, bbox_data in enumerate(page_bboxes[page_num]):
            bbox = bbox_data["bbox"]
            bbox_type = bbox_data["type"]
            text = bbox_data["text"]

            # 坐标转换：PDF坐标到图像坐标
            # PDF坐标系：左下角为原点，向上为正
            # 图像坐标系：左上角为原点，向下为正
            x0, y0, x1, y1 = bbox

            # 转换为图像坐标（考虑放大倍数）
            img_x0 = x0
            img_y0 = y0 # 翻转Y轴
            img_x1 = x1
            img_y1 = y1  # 翻转Y轴

            # 创建矩形框
            width = img_x1 - img_x0
            height = img_y1 - img_y0

            color = type_colors.get(bbox_type, 'gray')
            rect = patches.Rectangle(
                (img_x0, img_y0), width, height,
                linewidth=2, edgecolor=color, facecolor='none', alpha=0.8
            )
            ax.add_patch(rect)

            # 添加标签
            label = f"{bbox_type}"
            ax.text(img_x0, img_y0 - 10, label,
                   fontsize=8, color=color, fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))

        # 设置坐标轴
        ax.set_xlim(0, img_width)
        ax.set_ylim(img_height, 0)  # 翻转Y轴以匹配图像坐标系
        ax.axis('off')

        # 添加图例
        legend_elements = [patches.Patch(color=color, label=type_name)
                          for type_name, color in type_colors.items()]
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(1, 1))

        # 保存图像
        output_file = output_path / f"page_{page_num}_with_bboxes.png"
        plt.tight_layout()
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()

        print(f"页面 {page_num} 已保存到: {output_file}")

    pdf_doc.close()
    print(f"所有页面处理完成，图像保存在: {output_path}")


# 执行PDF可视化
print("\n开始PDF bbox可视化...")
visualize_pdf_with_bboxes(f, doc_htmls)
print("PDF可视化完成！")


