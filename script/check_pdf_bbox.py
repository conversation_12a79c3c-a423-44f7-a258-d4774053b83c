#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio
from pathlib import Path

import pymupdf

from engine.es import es
from engine.rdb import g, load_session_context
from controller.repository import Doc, get_index
from controller.parser.chunker.base import ChunkType
from engine.file_system import minio_client, repository_bucket


@load_session_context
async def get_data(doc_id: int):
    doc = await Doc.get_one(doc_id=doc_id)
    index = get_index([doc["repo_id"]])

    source = await es.get_source(
        index=index,
        id=doc_id,
        source_includes=["chunks.type_", "chunks.bboxes"]
    )
    local_path = Path(f"./{doc_id}/{doc_id}.pdf")
    if not local_path.exists():
        local_path.parent.mkdir(parents=True, exist_ok=True)
        minio_client.fget_object(
            bucket_name=repository_bucket,
            object_name=Path(doc["path"]).with_suffix(".pdf").as_posix(),
            file_path=local_path
        )
    return local_path, source


def visualize_pdf_with_bboxes(local_pdf_path: Path, doc_source: dict):
    # 定义不同chunk_type的颜色映射 (RGB值，范围0-1)
    color_map = {
        ChunkType.paragraph: (0, 0, 1),      # 蓝色 - 段落
        ChunkType.toc: (0, 1, 0),            # 绿色 - 目录
        ChunkType.doc_title: (1, 0, 0),      # 红色 - 文档标题
    }

    # 定义类型标签映射
    label_map = {
        ChunkType.paragraph: "段落",
        ChunkType.toc: "目录",
        ChunkType.doc_title: "标题",
        "default": "未知"
    }

    with pymupdf.open(local_pdf_path.as_posix()) as pdf_doc:
        page_doc = {i+1: p for i, p in enumerate(pdf_doc)}

        for chunk in doc_source["chunks"]:
            chunk_type = ChunkType(chunk.get("type_", ChunkType.paragraph.value))

            # 获取对应的颜色和标签
            color = color_map[chunk_type]

            for page_bbox in chunk["bboxes"]:
                bbox = page_bbox["bbox"]
                page_num = page_bbox["page"]

                # 绘制边框
                shape = page_doc[page_num].new_shape()
                shape.draw_rect(bbox)
                shape.finish(color=color, width=2)
                shape.commit()

                # 添加类型标签
                # 在bbox的左上角添加文本标签
                text_point = pymupdf.Point(bbox[0], bbox[1] - 5)  # 稍微向上偏移
                page_doc[page_num].insert_text(
                    text_point,
                    f"[{chunk_type.value}]",
                    fontsize=8,
                    color=color
                )

        pdf_doc.save((local_pdf_path.parent / f"{local_pdf_path.stem}_bbox.pdf").as_posix())


if __name__ == '__main__':
    local_pdf_path, chunk_bboxes = asyncio.run(get_data(5893))
    visualize_pdf_with_bboxes(local_pdf_path, chunk_bboxes)