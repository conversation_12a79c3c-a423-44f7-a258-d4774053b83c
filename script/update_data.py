#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio

from engine.es import es_sync
from engine.rdb import load_session_context, g
from controller.repository import Doc, get_index


@load_session_context
async def update_tags():
    repo_id = -22
    index = get_index([repo_id])
    res = es_sync.search(
        index=index,
        query={"match_all": {}},
        size=10000,
        source_includes=["tags", "doc_id"],
        sort=[{"data_time": "asc"}]
    )
    doc_tags_mapping = {hit["_source"]["doc_id"]: hit["_source"]["tags"] for hit in res["hits"]["hits"]}

    _, docs = await Doc.get_list(repo_id=repo_id, page=1, per_page=10000)
    for doc in docs:
        await Doc.update(doc_id=doc["doc_id"], tags=doc_tags_mapping[doc["doc_id"]])
        await g.session.commit()
        es_sync.update(
            index=index,
            id=doc["doc_id"],
            body={
                "doc": {
                    "tenant_id": 1
                }
            }
        )


if __name__ == '__main__':
    asyncio.run(update_tags())