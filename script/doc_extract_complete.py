#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import asyncio

from engine.rdb import load_session_context
from controller.repository import <PERSON>
from tasks import broker, doc_extract

@load_session_context
async def extract_doc():
    _, docs = await Doc.get_list(page=1, per_page=1000)
    for doc in docs:
        source = await Doc.get_es_one(repo_id=doc["repo_id"], doc_id=doc["doc_id"], includes=["extract_result"])
        if source.get("extract_result"):
            continue
        else:
            await doc_extract.kiq(doc["doc_id"])
            print(f"发送任务: {doc['doc_id']}")


if __name__ == '__main__':
    asyncio.run(extract_doc())