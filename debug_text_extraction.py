#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试文本提取中的重复问题
"""

import re
from lxml import etree, html as lxmlhtml

def debug_extract_plain_text_from_html(html_content):
    """
    调试版本的文本提取，显示详细过程
    """
    if not html_content:
        return ""
    
    replace_one_white_pattern = re.compile(r"(?<![a-zA-Z\W0-9])\s(?![a-zA-Z\W0-9])")
    
    try:
        # 解析HTML
        tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())
        
        print("=== 调试文本提取过程 ===")
        print(f"HTML内容: {html_content[:200]}...")
        
        # 收集所有唯一的单元格文本，避免合并单元格重复
        unique_cell_texts = []
        processed_cells = set()
        
        # 按行处理
        for row_idx, row in enumerate(tree.xpath('.//tr')):
            print(f"\n--- 处理行 {row_idx} ---")
            for cell_idx, cell in enumerate(row.xpath('.//td | .//th')):
                # 获取单元格信息
                cell_text_content = cell.text_content().strip()
                colspan = cell.get('colspan', '1')
                rowspan = cell.get('rowspan', '1')
                
                print(f"单元格 {cell_idx}: 文本='{cell_text_content}', colspan={colspan}, rowspan={rowspan}")
                
                # 使用单元格的文本内容和属性作为唯一标识
                cell_id = (cell_text_content, colspan, rowspan)
                
                print(f"  单元格ID: {cell_id}")
                print(f"  是否已处理: {cell_id in processed_cells}")
                
                # 如果这个单元格还没有被处理过
                if cell_id not in processed_cells:
                    # 提取单元格内的所有文本，去除多余空白
                    cell_text_fragments = []
                    for text in cell.xpath('.//text()[normalize-space() != ""]'):
                        text = replace_one_white_pattern.sub("", text.strip())
                        if text:
                            cell_text_fragments.append(text)
                    
                    cell_text = " ".join(cell_text_fragments).replace("\n", "")
                    if cell_text.strip():
                        unique_cell_texts.append(cell_text.strip())
                        processed_cells.add(cell_id)
                        print(f"  ✅ 添加文本: '{cell_text.strip()}'")
                    else:
                        print(f"  ❌ 空文本，跳过")
                else:
                    print(f"  ⚠️  重复单元格，跳过")
        
        result = " ".join(unique_cell_texts)
        print(f"\n=== 最终结果 ===")
        print(f"提取的文本: {result}")
        print(f"总词数: {len(result.split())}")
        
        # 检查重复词汇
        words = result.split()
        word_counts = {}
        for word in words:
            word_counts[word] = word_counts.get(word, 0) + 1
        
        duplicates = {word: count for word, count in word_counts.items() if count > 1}
        if duplicates:
            print(f"发现重复词汇: {duplicates}")
        else:
            print("无重复词汇")
        
        return result
        
    except Exception as e:
        print(f"解析失败: {e}")
        return ""

def test_debug():
    """测试调试功能"""
    
    # 测试包含合并单元格的HTML
    test_html = """
    <table>
        <tbody>
            <tr>
                <td rowspan="3"><b><font>基础模块-机器人<br>Windows版</font></b></td>
                <td rowspan="2"><font>执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</font></td>
                <td><font>流程任务拉取</font></td>
                <td rowspan="3"><font>必选</font></td>
                <td rowspan="2"><font>个</font></td>
                <td rowspan="2"><font>45000</font></td>
                <td rowspan="2"><font>1</font></td>
            </tr>
            <tr>
                <td><font>流程任务运行</font></td>
            </tr>
            <tr>
                <td rowspan="2"><font>执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</font></td>
                <td><font>流程任务数据上报</font></td>
                <td rowspan="2"><font>个</font></td>
                <td rowspan="2"><font>30000</font></td>
                <td rowspan="2"><font>1</font></td>
            </tr>
        </tbody>
    </table>
    """
    
    result = debug_extract_plain_text_from_html(test_html)

if __name__ == "__main__":
    test_debug()
