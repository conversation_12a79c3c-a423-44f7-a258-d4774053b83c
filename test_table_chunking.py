#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进后的chunk_large_table_tag方法处理合并单元格的能力
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.controller.parser.chunker.html_chunker import HtmlChunker
from src.controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER

def test_merged_cells_table():
    """测试包含合并单元格的表格处理"""
    
    # 您提供的测试HTML
    test_html = """<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Table</title>
</head>
<body>
<hr>
	<p></p><center>
		<h1>Overview</h1>
		<a href="#table0">标准产品报价（永久授权）</a><br>
		<a href="#table1">标准产品报价（年费制）</a><br>
		<a href="#table2">增值服务</a><br>

	</center>
<hr>
<a name="table0"><h1>Sheet 1: <em>标准产品报价（永久授权）</em></h1></a>
<table>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<colgroup></colgroup>
	<tr>
		<td><b><font><br></font></b></td>
		<td><b><font><br></font></b></td>
		<td><b><font><br></font></b></td>
		<td><b><font><br></font></b></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
	</tr>
	<tr>
		<td colspan="2" rowspan="3"><b><font><br><img src="1_html_b4e0157c1e7c863a.png" hspace="33" vspace="14">
		</font></b></td>
		<td colspan="3" rowspan="3"><b><font><br><img src="1_html_af8480bcd7f2a3e0.png" hspace="91" vspace="12">
		</font></b></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
	</tr>
	<tr>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
	</tr>
	<tr>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><b><font><br></font></b></td>
	</tr>
	<tr>
		<td><font>客户名称</font></td>
		<td colspan="2"><font><br></font></td>
		<td><font>报价单位名称</font></td>
		<td sdnum="1033;0;#,##0_ "><font>达观数据有限公司</font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
	</tr>
	<tr>
		<td><font>报价单位联系地址</font></td>
		<td colspan="2"><font><br></font></td>
		<td><font>报价单位联系人</font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
	</tr>
	<tr>
		<td><font>报价日期</font></td>
		<td colspan="2"><font><br></font></td>
		<td><font>报价有效期至</font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
		<td><font><br></font></td>
		<td sdnum="1033;0;#,##0_ "><font><br></font></td>
	</tr>
	<tr>
		<td colspan="8"><b><font>RPA软件产品报价</font></b></td>
		</tr>
	<tr>
		<td><b><font>产品名称</font></b></td>
		<td><b><font>类型</font></b></td>
		<td><b><font>描述</font></b></td>
		<td><b><font>必选/可选</font></b></td>
		<td><b><font>单位</font></b></td>
		<td sdnum="1033;0;#,##0_ "><b><font>单价（元）</font></b></td>
		<td><b><font>数量</font></b></td>
		<td sdnum="1033;0;#,##0_ "><b><font>总价（元）</font></b></td>
	</tr>
	<tr>
		<td rowspan="14"><b><font>基础模块-机器人<br>Windows版</font></b></td>
		<td rowspan="7"><font>执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</font></td>
		<td><font>流程任务拉取</font></td>
		<td rowspan="14"><font>必选</font></td>
		<td rowspan="7"><font>个</font></td>
		<td rowspan="7" sdval="45000" sdnum="1033;0;#,##0_ "><font>  </font></td>
		<td rowspan="7"><font><br></font></td>
		<td rowspan="7" sdnum="1033;0;#,##0_ "><font><br></font></td>
	</tr>
	<tr>
		<td><font>流程任务运行</font></td>
		</tr>
	<tr>
		<td><font>流程任务数据上报</font></td>
		</tr>
	<tr>
		<td><font>流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td><font>流程任务异常截图</font></td>
		</tr>
	<tr>
		<td><font>机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td><font>机器人远程控制</font></td>
		</tr>
	<tr>
		<td rowspan="7"><font>执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</font></td>
		<td><font>流程任务拉取</font></td>
		<td rowspan="7"><font>个</font></td>
		<td rowspan="7" sdval="30000" sdnum="1033;0;#,##0_ "><font>30,000 </font></td>
		<td rowspan="7"><font><br></font></td>
		<td rowspan="7" sdnum="1033;0;#,##0_ "><font><br></font></td>
	</tr>
	<tr>
		<td><font>流程任务运行</font></td>
		</tr>
	<tr>
		<td><font>流程任务数据上报</font></td>
		</tr>
	<tr>
		<td><font>流程任务执行录屏</font></td>
		</tr>
	<tr>
		<td><font>流程任务异常截图</font></td>
		</tr>
	<tr>
		<td><font>机器人运行环境监控及数据上报</font></td>
		</tr>
	<tr>
		<td><font>机器人远程控制</font></td>
		</tr>
</table>
</body>
</html>"""

    print("开始测试改进后的chunk_large_table_tag方法...")
    
    # 创建chunker实例，设置较小的max_tokens以触发表格分块
    chunker = HtmlChunker(tokenizer=BGE_M3_TOKENIZER, max_tokens=512, max_tolerance_tokens=1024)
    
    try:
        # 执行分块
        chunks = chunker.chunk(html_content=test_html)
        
        print(f"\n总共生成了 {len(chunks)} 个chunks")
        
        # 输出每个chunk的信息
        for i, chunk in enumerate(chunks):
            print(f"\n=== Chunk {i+1} ===")
            print(f"Token数量: {chunk.token_counts}")
            print(f"节点数量: {len(chunk.nodes)}")
            
            for j, node in enumerate(chunk.nodes):
                print(f"\n--- Node {j+1} (Tag: {node.tag}) ---")
                print(f"纯文本内容: {node.plain_content[:200]}...")
                if node.tag.startswith('table'):
                    print(f"HTML内容: {node.html_content[:500]}...")
                    
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_merged_cells_table()
