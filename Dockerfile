FROM python:3.11-slim

RUN mkdir /app
ARG code_dir="/app"
WORKDIR ${code_dir}

ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH "${PYTHONPATH}:${code_dir}"

RUN echo "deb https://mirrors.huaweicloud.com/debian bookworm main contrib non-free" > /etc/apt/sources.list \
 && echo "deb https://mirrors.huaweicloud.com/debian bookworm-updates main contrib non-free" >> /etc/apt/sources.list \
 && echo "deb https://mirrors.huaweicloud.com/debian-security bookworm-security main contrib non-free" >> /etc/apt/sources.list \
 && apt-get update \
 && apt-get install -y vim curl libreoffice fonts-liberation fonts-dejavu-core

# CODE
COPY ./resources /resources
COPY ./requirements.txt ${code_dir}/
RUN rm -rf /etc/pip.conf && \
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --no-cache
COPY . ${code_dir}

WORKDIR ${code_dir}/src
