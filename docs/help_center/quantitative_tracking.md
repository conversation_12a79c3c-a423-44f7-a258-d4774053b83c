# 量化追踪

---

量化追踪是一个基于已有摘要结果，每日根据配置的数据管道，重新计算每一项的量化结果和转化为一个可量化的二维面板，以方便进行进行长期的跟踪对比，实现一站式的监控和跟踪。

## 新建分析面板

### 基本信息

* **面板名称**：显示的面板名称。
* **更新时间**：可选择每日定时任务的数据。如需根据每日生成的摘要进行处理，请为摘要任务的生成预留一定时间。
* **分析方向**：分析任务的整体大方向，如 '分析A股股票投资价值'、 '分析某国政治局势'。


### 摘要配置

#### **组合摘要**

以笛卡尔积形式构建多层级的数据分析管道。  

> 例如，第一层有`全球宏观经济`等1个摘要任务，第二层`国内宏观经济`等1个摘要任务，第三层有`宁德时代`和`隆基绿能`等2个摘要任务。
则会生成1*1*2=2个数据分析管道：
> 1. 全球宏观经济+国内宏观经济+宁德时代
> 2. 全球宏观经济+国内宏观经济+隆基绿能
每个管道都会生成一个数据项(面板的行)，您可在预览中的`组合摘要`部分查看使用的组合摘要数量

#### **补充摘要**

在组合摘要的基础上，额外根据标签匹配增加的摘要任务。

> 例如当`宁德时代`具有 锂电池 标签时，但`隆基绿能`不带有 锂电池 的标签，那么标签为 锂电池 的`锂电池市场分析研究`的，只会附加到`宁德时代`的管道中，不会附加到`隆基绿能`中。

您可在预览中的`补充摘要`部分查看对应项使用的补充Agent数量

### 量化要求

* **字段名称**：分析字段的名称，会作为表格的列头显示
* **字段类型**：分析字段的类型，目前分为`数值`类型和`枚举`类型
* **(数值)最小值**：数值的最小值要求
* **(数值)最大值**：数值的最大值要求
* **(枚举)枚举内容**：枚举字段的要求
* **分析要求**：对该字段的分析内容如何设定

### 面板预览

面板预览会显示当任务开始执行时，具体的表格形态，帮助您理解整体设置是否达到预期。

## 查看分析面板

分析面板创建后，在执行第一次任务后，会以二维面板的方式呈现量化表格。如果有超过一次的任务执行，数值任务会表现本次和上次的分数变化情况（上涨或下跌）。

## 画布
您可以选用支持的图表（折线图、柱状图、饼图等）将面板的数据进行描绘，方便以图表方式进行可以化展示。