# 模型配置

---

您可注册大模型到平台中，并在各个模块中使用。此功能仅超级管理员可用。
在当前版本，必须经过私有化部署和团队验证后，方可新增和修改模型配置。

* **最大输入Tokens**：此配置影响模型的输入长度。除了避免过长导致的模型报错外，也影响部分功能的实现方式。
    * 单文档问答：如果单文档的全文长度小于模型输入长度，则会传入全量文件进行问答，方便实现非RAG标准任务（如描述性、关联性任务）
    * 知识问答：过小的长度可能使排名靠后的片段无法进入大模型，导致部分召回效果  
* **批量任务数**：此配置影响调用此模型的同时并发数
* **使用场景**：配置模型支持的能力，包括：
    * 函数调用：是否支持function_calling，暂未有影响，后续影响NL2API
    * json输出：是否支持json output能力，影响输出结论时的具体实现
    * 结构化输出：是否支持structured_output，影响输出结论时的具体实现
    * 问答：问答功能中是否可使用此模型
    * 推理：是否支持thinking
    * 深入研究：深入研究(deepresearch)功能中是否可使用此模型
    * 文档分析：问答功能中是否可使用此模型
    * 摘要任务：摘要任务功能中是否可使用此模型
    * 量化追踪：量化追踪功能中是否可使用此模型
