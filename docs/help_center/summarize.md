### 摘要总结

---

摘要总结是对知识库内选定范围的数据，使用Agent进行全面的阅读和理解，并输出用户要求的分析结果。

## 新建摘要任务

### 单独构建

输入相关字段，进行一个摘要任务的构建，包括：

#### 基本信息
* 名称：显示名称
* 标签：标记特征，后续在量化追踪中便于批量筛选
* 执行时间：`单次` 只执行一次 `每天` 选定执行时间，每日对最新数据执行

#### 数据范围
* 知识库：将在选定的知识库下进行筛选
* 数据筛选：
  * 包含关键词：在文档标题、正文、作者、来源中的关键词
  * 最近N天：最新多少天内上传的数据
  * 最新N条：最近多少条数据

#### 模型配置
* 使用模型：选择使用的大模型进行摘要，建议使用`Deepseek-V3 250324`或效果更好的模型
* 任务要求：任务要求提示词

### 批量构建

批量构建用于在不同的关键词筛选下，使用同一个任务要求，可以一次性通过批量关键词配置构建多个摘要任务。

与单独构建摘要任务有以下几点不同：
1. 数据范围仅允许组合关键词实现，每行的关键词都会构建一个独立的摘要任务
2. 无法指定每个摘要任务的名称，名称将由加号组合同行的关键词自动形成名称
3. 无法指定每个摘要任务的标签，同行的每个关键词都是该摘要任务的标签

## 摘要结果

左侧展示摘要任务生成的Markdown格式结果，每个icon在鼠标悬浮时会显示前序论点的相关论据。  
右侧分别显示生成时间、汇总数据量、文档输入的总Tokens和摘要生成后的输出Tokens，使用的关联文档等，关联文档可以跳转至文档详情页。
