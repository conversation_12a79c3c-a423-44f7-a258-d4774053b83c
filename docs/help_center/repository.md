# 知识库

---

知识库可接受用户的上传文件到不同的库中并进行解析，以进一步的分析和使用其他AI功能。

## 新建知识库

**知识库名称**：显示名称
**知识库类型**：影响可见和使用权限类型，包括：
    * `公共`：租户下所有用户可见
    * `私有`：仅自己可见
**分析模型**：使用分析的大模型

## 知识库

### 分析图表

* **情绪因子走势**：知识库内文档分析后的情绪因子走势，对于上传文档，数据时间为文档的上传时间；对于API文档，数据时间为传入参数的时间。
* **讨论实体TOP5**：知识库内所有文档分析后的抽取出实体的统计结果。
* **关键词Top10**：知识库内所有文档分析后的高词频关键词的统计结果。

### 文档展示
目前对于doc/docx/ppt/pptx/pdf等格式，使用pdf进行展示。  
对于其他支持的格式，使用html进行展示。

### AI分析

此功能必须在知识库配置分析模型，否则不会展示。

* **使用模型**：分析此文档使用的模型名称。
* **思考过程**：大模型在输出分析结果时的思考过程展示。
* **摘要**：大模型分析后得到的文档摘要
* **情绪因子**：大模型分析后得到的情绪因子，总区间为[-100, 100]。各分类区间：
  * 正面: [-100, <-34)
  * 中性: [-34, 35)
  * 负面: [35, 100]
* **观点**：大模型分析后得到正面/负面观点。
* **实体**：大模型分析后抽取出的实体，一般围绕客观存在的实体或概念上的实体。
* **关键信息**：大模型分析后总结的关键信息。

### 文档问答

可对单文档进行文档问答

> 如果文档的文本内容长度小于所选择的大模型输入长度，则会传入全文给大模型，以支持对文档进行描述性问答的需求。但如果文本长度大于所选择的大模型长度，则使用RAG方案将问题的相关片段给到大模型。