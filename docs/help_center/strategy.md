# 策略配置

---

租户可以根据自身业务文档的风格进行相关的策略配置，以达到更好的解析效果。  
默认配置是常规情况的最优解，请在调整前阅读此文档以清楚功能影响。

### 解析配置

* **强制OCR**：是否对文档强制进行OCR。等同于视觉解析优先，能较好的解决图文混排，多层文档覆盖等各类问题，但会增加解析时间，默认开启。
* **图片缩放比例**：对图片进行扩增以保证OCR效果。默认2.0。
* **解析文档图片**：是否需要解析文档中的图片。由于一般文档中的图片信息含量不高，开启后会增加解析时间，默认关闭。
* **提取表结构**：是否以高精度进行表格提取。开启后会增加表格解析的效果，同时增加解析时间，默认开启。
* **提取Latex表达式**：是否进行latex表达式提取。开启后会增加latex解析的效果，同时增加解析时间，默认开启。

### 问答检索配置
> 在经过各类方案的尝试后，我们的RAG最终使用二步式召回，第一步先筛选文档，第二步找到片段。  
> 在这个过程中会通过问题改写/扩展的情况，混合多类/多个BM25策略和Embedding策略，得到最终结果。在默认配置中，BM25和策略权重相等，已经配平。常规不建议修改。

* **最终保留片段数**：最终保留的片段数量，默认20，如果在问答记录中的详情中发现文档已召回，相关的片段在最终结果没有出现，可以考虑增大此配置。不建议超过40。
* **BM25算法权重**：影响混合检索(bm25+embedding算法)BM25算法的权重，默认为1。
* **向量算法权重**：影响混合检索(bm25+embedding算法)Embedding算法的权重，默认为1。
* **最大召回文档数量**：在召回文档时保留的最大文档召回数，默认为10。
* **Rerank模型阈值**：rerank模型的阈值。如果在问答记录中的详情中发现文档已召回，相关的片段在最终结果没有出现，可以考虑下调此配置，默认为0.8，不建议低于0.7。
* **Rerank模型**：指定的rerank模型，默认为bge-reranker-v2-m3。
* 问题改写模型：指定的问题改写模型，默认为qwen3:30b-a3b-instruct。