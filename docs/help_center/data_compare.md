# 数据对比

---

基于知识库的AI分析能力，用户可在数据对比中对筛选范围内的知识库数据，进一步的进行以文字、图表的方式进行对比和AI数理分析。  
支持以下子项的输出显示

## AI解读
获取数据后，AI解读会通过数据的数值形态，从数理分析的角度上进行推断。一般包括`统计分析`、`数据总结`、`建议或关注点`三个部分。  
如果数据只有`基本组`，那么AI解读会着重于对单组数据的数理分析；如果数据同时包含`基础组`和`对照组`，那么AI解读会着重于对两组数据的对比分析。

## 情绪因子
根据每篇文档的AI分析抽取出的情绪因子，对情绪的分布和日均趋势进行相对应统计图表展示。  

## 正面观点
### 词云和关键词
根据每篇文档的AI分析抽取出的正面观点，再次进行关键词的提取和聚合，获得单组下的词频，构建为词云和关键词列表。  
相比通过内容词频获取的关键词，此模块更能体现出文档中重要正面内容下的核心词汇。

### 观点数趋势
按日展示的日均正面观点数。  
相比情绪因子，聚合观点数的方式能更好的处理正面情绪中大量正面语法手法反复描述同一观点，导致情绪分虚高的情况。  

## 负面观点
### 词云和关键词
根据每篇文档的AI分析抽取出的负面观点，再次进行关键词的提取和聚合，获得单组下的词频，构建为词云和关键词列表。  
相比通过内容词频获取的关键词，此模块更能体现出文档中重要负面内容下的核心词汇。

### 观点数趋势
按日展示的日均负面观点数。  
相比情绪因子，聚合观点数的方式能更好的处理负面情绪中大量负面语法手法反复描述同一观点，导致情绪分虚高的情况。

## 相关实体
根据每篇文档的AI分析抽取出的实体，对实体的出现次数进行统计和展示。
能够较好的体现出近期舆情讨论的热点围绕某类实体等。

## 相关关键词
根据每篇文档通过词频和磁性抽取出的关键词，对关键词的出现次数进行统计和展示。  
相对传统，但稳定有效的关键信息获取方式。
