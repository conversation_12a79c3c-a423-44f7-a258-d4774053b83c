#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化后的表格分块功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lxml import etree, html as lxmlhtml

class SimplifiedTableChunker:
    """简化的表格分块器，用于测试"""
    
    def _extract_column_headers(self, header_rows):
        """提取列头信息"""
        if not header_rows:
            return []
        
        headers = []
        for cell in header_rows[0].xpath('.//th | .//td'):
            header_text = cell.text_content().strip()
            if header_text:
                headers.append(header_text)
        return headers

    def _analyze_merged_cells_and_group(self, data_rows):
        """分析合并单元格并分组相关行"""
        if not data_rows:
            return []
        
        groups = []
        processed_rows = set()
        
        for i, row in enumerate(data_rows):
            if i in processed_rows:
                continue
                
            # 找到当前行涉及的所有行（由于rowspan）
            group_rows = [row]
            max_rowspan = 1
            
            # 检查当前行的所有单元格的rowspan
            for cell in row.xpath('.//td'):
                rowspan = int(cell.get('rowspan', 1))
                max_rowspan = max(max_rowspan, rowspan)
            
            # 添加受rowspan影响的后续行
            for j in range(i + 1, min(i + max_rowspan, len(data_rows))):
                if j not in processed_rows:
                    group_rows.append(data_rows[j])
                    processed_rows.add(j)
            
            processed_rows.add(i)
            groups.append(group_rows)
        
        return groups

    def _build_complete_table_html(self, header_rows, group_rows):
        """构建完整的table HTML"""
        table_parts = []
        
        # 添加表头
        if header_rows:
            for header_row in header_rows:
                header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
                table_parts.append(header_html)
        
        # 添加数据行
        for row in group_rows:
            row_html = lxmlhtml.tostring(row, encoding="unicode", pretty_print=False)
            table_parts.append(row_html)
        
        # 组合成完整的table
        tbody_content = "".join(table_parts)
        return f"<table><tbody>{tbody_content}</tbody></table>"

    def _extract_structured_text(self, column_headers, group_rows):
        """提取结构化纯文本，格式：行头 列头：内容"""
        if not group_rows:
            return ""

        # 提取行头（第一列的内容，通常是主要标识）
        row_header = ""
        first_row = group_rows[0]
        first_cells = first_row.xpath('.//td')
        if first_cells:
            row_header = first_cells[0].text_content().strip()

        # 构建结构化文本
        text_parts = []

        # 添加行头作为主标识
        if row_header:
            text_parts.append(row_header)

        # 按列收集唯一内容
        if column_headers and len(column_headers) > 1:
            for i, header in enumerate(column_headers[1:], 1):  # 跳过第一列（行头）
                # 收集该列的所有唯一内容
                column_content = set()
                for row in group_rows:
                    cells = row.xpath('.//td')
                    if i < len(cells):
                        content = cells[i].text_content().strip()
                        if content and content != row_header:
                            column_content.add(content)

                if column_content:
                    # 将内容按字母数字排序，确保一致性
                    sorted_content = sorted(column_content)
                    text_parts.append(f"{header}：{' '.join(sorted_content)}")
        else:
            # 如果没有列头，收集所有唯一内容
            all_texts = set()
            for row in group_rows:
                for cell in row.xpath('.//td'):
                    cell_text = cell.text_content().strip()
                    if cell_text and cell_text != row_header:
                        all_texts.add(cell_text)

            if all_texts:
                text_parts.extend(sorted(all_texts))

        return " ".join(text_parts)

def test_simplified_chunking():
    """测试简化的分块功能"""
    
    # 测试HTML - 包含合并单元格的产品表格
    test_html = """
    <table>
        <tr>
            <th>产品名称</th>
            <th>类型</th>
            <th>描述</th>
            <th>必选/可选</th>
            <th>单位</th>
            <th>单价（元）</th>
            <th>数量</th>
            <th>总价（元）</th>
        </tr>
        <tr>
            <td rowspan="3">基础模块-机器人</td>
            <td rowspan="2">执行器浮动授权</td>
            <td>流程任务拉取</td>
            <td rowspan="3">必选</td>
            <td rowspan="2">个</td>
            <td rowspan="2">45000</td>
            <td rowspan="2">1</td>
            <td rowspan="2">45000</td>
        </tr>
        <tr>
            <td>流程任务运行</td>
        </tr>
        <tr>
            <td>执行器固定授权</td>
            <td>流程任务数据上报</td>
            <td>个</td>
            <td>30000</td>
            <td>1</td>
            <td>30000</td>
        </tr>
        <tr>
            <td rowspan="2">开发平台</td>
            <td>设计器授权</td>
            <td>可视化流程开发</td>
            <td>可选</td>
            <td>个</td>
            <td>60000</td>
            <td>1</td>
            <td>60000</td>
        </tr>
        <tr>
            <td>编辑器授权</td>
            <td>代码流程开发</td>
            <td>可选</td>
            <td>个</td>
            <td>50000</td>
            <td>1</td>
            <td>50000</td>
        </tr>
    </table>
    """
    
    print("=== 测试简化的表格分块功能 ===\n")
    
    chunker = SimplifiedTableChunker()
    
    # 解析HTML
    tree = etree.fromstring(test_html, lxmlhtml.HTMLParser())
    table = tree.xpath('//table')[0]
    
    # 提取表头和数据行
    header_rows = table.xpath('.//tr[th]')
    data_rows = table.xpath('.//tr[td]')
    
    print(f"表头行数: {len(header_rows)}")
    print(f"数据行数: {len(data_rows)}")
    
    # 提取列头信息
    column_headers = chunker._extract_column_headers(header_rows)
    print(f"列头: {column_headers}")
    
    # 分析合并单元格并分组
    logical_groups = chunker._analyze_merged_cells_and_group(data_rows)
    print(f"逻辑组数: {len(logical_groups)}")
    
    # 为每个逻辑组创建table块
    for group_index, group_rows in enumerate(logical_groups):
        print(f"\n=== 组 {group_index + 1} ===")
        print(f"包含行数: {len(group_rows)}")
        
        # 构建完整的table HTML
        table_html = chunker._build_complete_table_html(header_rows, group_rows)
        print(f"Table HTML长度: {len(table_html)} 字符")
        
        # 提取结构化纯文本（列头：行头格式）
        structured_text = chunker._extract_structured_text(column_headers, group_rows)
        print(f"结构化文本: {structured_text}")
        
        # 显示HTML预览
        print(f"HTML预览: {table_html[:200]}...")
        
        # 检查重复
        words = structured_text.split()
        word_counts = {}
        for word in words:
            word_counts[word] = word_counts.get(word, 0) + 1
        
        duplicates = {word: count for word, count in word_counts.items() if count > 1}
        if duplicates:
            print(f"❌ 发现重复词汇: {duplicates}")
        else:
            print(f"✅ 无重复词汇 (总词数: {len(words)})")

if __name__ == "__main__":
    test_simplified_chunking()
