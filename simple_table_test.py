#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的表格合并单元格处理测试
"""

from lxml import etree, html as lxmlhtml

def build_table_matrix(data_rows):
    """
    构建表格矩阵来处理合并单元格
    返回一个矩阵，记录每个位置的单元格信息
    """
    if not data_rows:
        return []
        
    # 预估最大列数
    max_cols = 0
    for row in data_rows:
        cells = row.xpath('.//td')
        col_count = sum(int(cell.get('colspan', 1)) for cell in cells)
        max_cols = max(max_cols, col_count)
    
    # 初始化矩阵
    matrix = []
    for row_idx, row in enumerate(data_rows):
        if row_idx >= len(matrix):
            matrix.append([None] * max_cols)
        
        cells = row.xpath('.//td')
        col_idx = 0
        
        for cell in cells:
            # 找到下一个空位置
            while col_idx < len(matrix[row_idx]) and matrix[row_idx][col_idx] is not None:
                col_idx += 1
            
            if col_idx >= len(matrix[row_idx]):
                break
                
            colspan = int(cell.get('colspan', 1))
            rowspan = int(cell.get('rowspan', 1))
            
            # 填充当前单元格占用的所有位置
            for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                # 确保有足够的行
                while r >= len(matrix):
                    matrix.append([None] * max_cols)
                
                for c in range(col_idx, min(col_idx + colspan, max_cols)):
                    if c < len(matrix[r]):
                        matrix[r][c] = {
                            'cell': cell,
                            'row_idx': row_idx,
                            'col_idx': col_idx,
                            'colspan': colspan,
                            'rowspan': rowspan,
                            'is_origin': (r == row_idx and c == col_idx),
                            'text': cell.text_content().strip()
                        }
            
            col_idx += colspan
    
    return matrix

def group_logical_rows(table_matrix, data_rows):
    """
    根据合并单元格将行分组为逻辑行组
    """
    if not table_matrix or not data_rows:
        return []
        
    groups = []
    processed_rows = set()
    
    for row_idx in range(len(data_rows)):
        if row_idx in processed_rows:
            continue
            
        # 找到当前行涉及的所有行（由于rowspan）
        involved_rows = set([row_idx])
        
        if row_idx < len(table_matrix):
            for cell_info in table_matrix[row_idx]:
                if cell_info and cell_info['is_origin']:
                    rowspan = cell_info['rowspan']
                    for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                        involved_rows.add(r)
        
        groups.append(sorted(involved_rows))
        processed_rows.update(involved_rows)
    
    return groups

def test_table_parsing():
    """测试表格解析功能"""
    
    # 简化的测试HTML
    test_html = """
    <table>
        <tr>
            <td colspan="2" rowspan="3">合并单元格A</td>
            <td colspan="3" rowspan="3">合并单元格B</td>
            <td>单元格C1</td>
            <td>单元格D1</td>
        </tr>
        <tr>
            <td>单元格C2</td>
            <td>单元格D2</td>
        </tr>
        <tr>
            <td>单元格C3</td>
            <td>单元格D3</td>
        </tr>
        <tr>
            <td>客户名称</td>
            <td colspan="2">空白</td>
            <td>报价单位名称</td>
            <td>达观数据有限公司</td>
            <td>空白1</td>
            <td>空白2</td>
        </tr>
        <tr>
            <td rowspan="14">基础模块-机器人</td>
            <td rowspan="7">执行器浮动授权</td>
            <td>流程任务拉取</td>
            <td rowspan="14">必选</td>
            <td rowspan="7">个</td>
            <td rowspan="7">45000</td>
            <td rowspan="7">1</td>
        </tr>
        <tr>
            <td>流程任务运行</td>
        </tr>
        <tr>
            <td>流程任务数据上报</td>
        </tr>
    </table>
    """
    
    print("开始测试表格合并单元格解析...")
    
    # 解析HTML
    tree = etree.fromstring(test_html, lxmlhtml.HTMLParser())
    table = tree.xpath('//table')[0]
    data_rows = table.xpath('.//tr')
    
    print(f"总共找到 {len(data_rows)} 行")
    
    # 构建矩阵
    matrix = build_table_matrix(data_rows)
    
    print(f"\n构建的矩阵大小: {len(matrix)} x {len(matrix[0]) if matrix else 0}")
    
    # 打印矩阵结构
    print("\n表格矩阵结构:")
    for i, row in enumerate(matrix):
        print(f"行 {i}: ", end="")
        for j, cell in enumerate(row):
            if cell:
                if cell['is_origin']:
                    print(f"[{cell['text'][:10]}({cell['colspan']}x{cell['rowspan']})]", end=" ")
                else:
                    print("[占位]", end=" ")
            else:
                print("[空]", end=" ")
        print()
    
    # 分组逻辑行
    groups = group_logical_rows(matrix, data_rows)
    
    print(f"\n逻辑行分组结果:")
    for i, group in enumerate(groups):
        print(f"组 {i+1}: 行 {group}")
        
        # 显示每组的内容
        group_content = []
        for row_idx in group:
            if row_idx < len(data_rows):
                row_text = data_rows[row_idx].text_content().strip()
                if row_text:
                    group_content.append(row_text)
        
        if group_content:
            print(f"  内容: {' | '.join(group_content)}")
        print()

if __name__ == "__main__":
    test_table_parsing()
