#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试完整的表格分块流程，验证改进后的文本提取
"""

import re
from lxml import etree, html as lxmlhtml

class MockChunker:
    """模拟Chunker类的核心方法"""
    
    def __init__(self):
        self.replace_one_white_pattern = re.compile(r"(?<![a-zA-Z\W0-9])\s(?![a-zA-Z\W0-9])")
        self.remove_html_blank = re.compile(r'(?<=>)\s+(?=<)')
    
    def _build_table_matrix(self, data_rows):
        """构建表格矩阵来处理合并单元格"""
        if not data_rows:
            return []
            
        # 预估最大列数
        max_cols = 0
        for row in data_rows:
            cells = row.xpath('.//td')
            col_count = sum(int(cell.get('colspan', 1)) for cell in cells)
            max_cols = max(max_cols, col_count)
        
        # 初始化矩阵
        matrix = []
        for row_idx, row in enumerate(data_rows):
            if row_idx >= len(matrix):
                matrix.append([None] * max_cols)
            
            cells = row.xpath('.//td')
            col_idx = 0
            
            for cell in cells:
                # 找到下一个空位置
                while col_idx < len(matrix[row_idx]) and matrix[row_idx][col_idx] is not None:
                    col_idx += 1
                
                if col_idx >= len(matrix[row_idx]):
                    break
                    
                colspan = int(cell.get('colspan', 1))
                rowspan = int(cell.get('rowspan', 1))
                
                # 填充当前单元格占用的所有位置
                for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                    # 确保有足够的行
                    while r >= len(matrix):
                        matrix.append([None] * max_cols)
                    
                    for c in range(col_idx, min(col_idx + colspan, max_cols)):
                        if c < len(matrix[r]):
                            matrix[r][c] = {
                                'cell': cell,
                                'row_idx': row_idx,
                                'col_idx': col_idx,
                                'colspan': colspan,
                                'rowspan': rowspan,
                                'is_origin': (r == row_idx and c == col_idx)
                            }
                
                col_idx += colspan
        
        return matrix

    def _group_logical_rows(self, table_matrix, data_rows):
        """根据合并单元格将行分组为逻辑行组"""
        if not table_matrix or not data_rows:
            return []
            
        groups = []
        processed_rows = set()
        
        for row_idx in range(len(data_rows)):
            if row_idx in processed_rows:
                continue
                
            # 找到当前行涉及的所有行（由于rowspan）
            involved_rows = set([row_idx])
            
            if row_idx < len(table_matrix):
                for cell_info in table_matrix[row_idx]:
                    if cell_info and cell_info['is_origin']:
                        rowspan = cell_info['rowspan']
                        for r in range(row_idx, min(row_idx + rowspan, len(data_rows))):
                            involved_rows.add(r)
            
            logical_structure = self._build_logical_structure(table_matrix, list(involved_rows))
            groups.append((sorted(involved_rows), logical_structure))
            processed_rows.update(involved_rows)
        
        return groups

    def _build_logical_structure(self, table_matrix, row_indices):
        """为一组行构建逻辑结构"""
        if not row_indices or not table_matrix:
            return []
            
        structure = []
        max_cols = len(table_matrix[0]) if table_matrix else 0
        
        for col_idx in range(max_cols):
            column_cells = []
            for row_idx in row_indices:
                if row_idx < len(table_matrix) and col_idx < len(table_matrix[row_idx]):
                    cell_info = table_matrix[row_idx][col_idx]
                    if cell_info and cell_info['is_origin']:
                        column_cells.append(cell_info)
            structure.append(column_cells)
        
        return structure

    def _reconstruct_table_html(self, data_rows, row_indices, logical_structure, header_html=""):
        """重构表格HTML，保持合并单元格的结构"""
        if not row_indices:
            return ""
            
        # 收集所有需要的单元格
        cells_to_include = set()
        for column_cells in logical_structure:
            for cell_info in column_cells:
                cells_to_include.add(id(cell_info['cell']))
        
        # 重构HTML
        reconstructed_rows = []
        for row_idx in row_indices:
            if row_idx >= len(data_rows):
                continue
                
            row = data_rows[row_idx]
            cells = row.xpath('.//td')
            
            # 只包含属于当前逻辑组的单元格
            filtered_cells = [cell for cell in cells if id(cell) in cells_to_include]
            
            if filtered_cells:
                # 重构行HTML
                row_html = "<tr>"
                for cell in filtered_cells:
                    cell_html = lxmlhtml.tostring(cell, encoding="unicode", pretty_print=False)
                    row_html += cell_html
                row_html += "</tr>"
                reconstructed_rows.append(row_html)
        
        return "".join(reconstructed_rows)

    def _extract_plain_text_from_html(self, html_content):
        """从HTML表格中提取纯文本，在不同单元格之间添加空格分隔，避免合并单元格导致的重复内容"""
        if not html_content:
            return ""

        try:
            # 解析HTML
            tree = etree.fromstring(html_content, lxmlhtml.HTMLParser())

            # 收集所有唯一的单元格文本，避免合并单元格重复
            unique_cell_texts = []
            processed_cells = set()

            # 按行处理
            for row in tree.xpath('.//tr'):
                for cell in row.xpath('.//td | .//th'):
                    # 使用单元格的文本内容和属性作为唯一标识
                    cell_id = (
                        cell.text_content().strip(),
                        cell.get('colspan', '1'),
                        cell.get('rowspan', '1')
                    )

                    # 如果这个单元格还没有被处理过
                    if cell_id not in processed_cells:
                        # 提取单元格内的所有文本，去除多余空白
                        cell_text_fragments = []
                        for text in cell.xpath('.//text()[normalize-space() != ""]'):
                            text = self.replace_one_white_pattern.sub("", text.strip())
                            if text:
                                cell_text_fragments.append(text)

                        cell_text = " ".join(cell_text_fragments).replace("\n", "")
                        if cell_text.strip():
                            unique_cell_texts.append(cell_text.strip())
                            processed_cells.add(cell_id)

            # 将所有唯一的单元格文本用空格连接
            return " ".join(unique_cell_texts)

        except Exception as e:
            return ""

def test_complete_chunking():
    """测试完整的表格分块流程"""
    
    # 使用您提供的HTML片段
    test_html = """
    <table>
        <tr>
            <td><b><font>产品名称</font></b></td>
            <td><b><font>类型</font></b></td>
            <td><b><font>描述</font></b></td>
            <td><b><font>必选/可选</font></b></td>
            <td><b><font>单位</font></b></td>
            <td><b><font>单价（元）</font></b></td>
            <td><b><font>数量</font></b></td>
            <td><b><font>总价（元）</font></b></td>
        </tr>
        <tr>
            <td rowspan="14"><b><font>基础模块-机器人<br>Windows版</font></b></td>
            <td rowspan="7"><font>执行器浮动授权，授权许可不绑定机器，只能配合控制中心使用。</font></td>
            <td><font>流程任务拉取</font></td>
            <td rowspan="14"><font>必选</font></td>
            <td rowspan="7"><font>个</font></td>
            <td rowspan="7"><font>45000</font></td>
            <td rowspan="7"><font>1</font></td>
            <td rowspan="7"><font>45000</font></td>
        </tr>
        <tr>
            <td><font>流程任务运行</font></td>
        </tr>
        <tr>
            <td><font>流程任务数据上报</font></td>
        </tr>
        <tr>
            <td><font>流程任务执行录屏</font></td>
        </tr>
        <tr>
            <td rowspan="7"><font>执行器固定授权许可，授权许可绑定机器，只能配合控制中心使用。</font></td>
            <td><font>流程任务拉取</font></td>
            <td rowspan="7"><font>个</font></td>
            <td rowspan="7"><font>30000</font></td>
            <td rowspan="7"><font>1</font></td>
            <td rowspan="7"><font>30000</font></td>
        </tr>
        <tr>
            <td><font>流程任务运行</font></td>
        </tr>
        <tr>
            <td><font>流程任务数据上报</font></td>
        </tr>
    </table>
    """
    
    print("=== 测试完整表格分块流程 ===\n")
    
    chunker = MockChunker()
    
    # 解析HTML
    tree = etree.fromstring(test_html, lxmlhtml.HTMLParser())
    table = tree.xpath('//table')[0]
    
    # 提取表头和数据行
    header_elements = table.xpath('.//tr[th]')
    data_rows = table.xpath(".//tr[td]")
    
    header_html = ""
    header_plain_text = ""
    
    if header_elements:
        header_row = header_elements[0]
        header_html = lxmlhtml.tostring(header_row, encoding="unicode", pretty_print=False)
        header_plain_text = chunker._extract_plain_text_from_html(f"<table><tbody>{header_html}</tbody></table>")
    
    print(f"找到表头: {bool(header_elements)}")
    print(f"数据行数: {len(data_rows)}")
    print(f"表头文本: {header_plain_text}\n")
    
    # 构建矩阵和分组
    table_matrix = chunker._build_table_matrix(data_rows)
    logical_row_groups = chunker._group_logical_rows(table_matrix, data_rows)
    
    print(f"逻辑行组数: {len(logical_row_groups)}\n")
    
    # 处理每个逻辑组
    for group_index, (row_indices, logical_structure) in enumerate(logical_row_groups):
        print(f"=== 组 {group_index + 1}: 行 {row_indices} ===")
        
        # 重构HTML
        reconstructed_html = chunker._reconstruct_table_html(
            data_rows, row_indices, logical_structure, header_html
        )
        
        # 构建完整表格HTML
        if header_html:
            combined_html = f"<table><tbody>{header_html}{reconstructed_html}</tbody></table>"
        else:
            combined_html = f"<table><tbody>{reconstructed_html}</tbody></table>"
        
        # 提取纯文本
        group_plain_text = chunker._extract_plain_text_from_html(combined_html)
        
        print(f"重构HTML长度: {len(reconstructed_html)} 字符")
        print(f"完整HTML长度: {len(combined_html)} 字符")
        print(f"提取的纯文本: {group_plain_text}")
        print(f"纯文本长度: {len(group_plain_text)} 字符")
        
        # 验证没有重复内容
        words = group_plain_text.split()
        unique_words = list(dict.fromkeys(words))  # 保持顺序的去重
        print(f"总词数: {len(words)}, 唯一词数: {len(unique_words)}")
        
        if len(words) != len(unique_words):
            print("⚠️  检测到重复词汇")
        else:
            print("✅ 无重复词汇")
        
        print()

if __name__ == "__main__":
    test_complete_chunking()
